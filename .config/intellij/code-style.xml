<code_scheme name="RequestAttrs" version="173">
  <option name="GENERATE_FINAL_LOCALS" value="true" />
  <option name="GENERATE_FINAL_PARAMETERS" value="true" />
  <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="1000" />
  <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="1000" />
  <JavaCodeStyleSettings>
    <option name="GENERATE_FINAL_LOCALS" value="true" />
    <option name="GENERATE_FINAL_PARAMETERS" value="true" />
    <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="1000" />
    <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="1000" />
    <option name="IMPORT_LAYOUT_TABLE">
      <value>
        <package name="com.netflix" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="javax" withSubpackages="true" static="false" />
        <package name="java" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="" withSubpackages="true" static="true" />
      </value>
    </option>
  </JavaCodeStyleSettings>
  <codeStyleSettings language="JAVA">
    <option name="INDENT_CASE_FROM_SWITCH" value="false" />
    <option name="ALIGN_MULTILINE_CHAINED_METHODS" value="true" />
    <option name="ALIGN_MULTILINE_BINARY_OPERATION" value="true" />
    <option name="ALIGN_MULTILINE_TERNARY_OPERATION" value="true" />
    <option name="BINARY_OPERATION_SIGN_ON_NEXT_LINE" value="true" />
    <option name="TERNARY_OPERATION_SIGNS_ON_NEXT_LINE" value="true" />
    <option name="KEEP_SIMPLE_CLASSES_IN_ONE_LINE" value="true" />
    <option name="IF_BRACE_FORCE" value="3" />
    <indentOptions>
      <option name="CONTINUATION_INDENT_SIZE" value="4" />
    </indentOptions>
  </codeStyleSettings>
</code_scheme>
