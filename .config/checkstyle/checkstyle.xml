<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
    "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
    "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">

<module name="Checker">

    <!-- Checks that a package-info.java file exists for each package.     -->
    <!-- See http://checkstyle.sf.net/config_javadoc.html#JavadocPackage -->
    <!--
    <module name="JavadocPackage">
      <property name="allowLegacy" value="true"/>
    </module>
    -->

    <!-- Checks whether files end with a new line.                        -->
    <!-- See http://checkstyle.sf.net/config_misc.html#NewlineAtEndOfFile -->
    <module name="NewlineAtEndOfFile"/>

    <!-- Checks that property files contain the same keys.         -->
    <!-- See http://checkstyle.sf.net/config_misc.html#Translation -->
    <module name="Translation"/>

    <!-- Checks for Size Violations.                    -->
    <!-- See http://checkstyle.sf.net/config_sizes.html -->
    <module name="FileLength"/>

    <!-- Checks for whitespace                               -->
    <!-- See http://checkstyle.sf.net/config_whitespace.html -->
    <module name="FileTabCharacter"/>

    <!-- Miscellaneous other checks.                   -->
    <!-- See http://checkstyle.sf.net/config_misc.html -->
    <module name="RegexpSingleline">
       <property name="format" value="\s+$"/>
       <property name="minimum" value="0"/>
       <property name="maximum" value="0"/>
       <property name="message" value="Line has trailing spaces."/>
       <property name="severity" value="info"/>
    </module>

    <module name="SuppressionFilter">
      <property name="file" value="${config_loc}/suppressions.xml"/>
    </module>

    <!-- Checks for Size Violations.                    -->
    <!-- See http://checkstyle.sf.net/config_sizes.html -->
    <module name="LineLength">
        <!-- what is a good max value? -->
        <property name="max" value="175"/>
        <!-- ignore lines like "$File: //depot/... $" -->
        <property name="ignorePattern" value="\$File.*\$"/>
        <property name="severity" value="info"/>
    </module>

    <module name="TreeWalker">

        <!-- Checks for Javadoc comments.                     -->
        <!-- See http://checkstyle.sf.net/config_javadoc.html -->
        <module name="JavadocMethod">
            <property name="accessModifiers" value="package"/>
            <property name="allowMissingParamTags" value="true"/>
            <property name="allowMissingReturnTag" value="true"/>
            <property name="tokens" value="METHOD_DEF,ANNOTATION_FIELD_DEF"/>
        </module>
        <module name="JavadocType">
          <property name="scope" value="package"/>
        </module>
        <module name="JavadocVariable">
          <property name="scope" value="package"/>
        </module>
        <module name="JavadocStyle">
          <property name="checkEmptyJavadoc" value="true"/>
        </module>

        <!-- Checks for Naming Conventions.                  -->
        <!-- See http://checkstyle.sf.net/config_naming.html -->
        <module name="ConstantName"/>
        <module name="LocalFinalVariableName"/>
        <module name="LocalVariableName"/>
        <module name="MemberName"/>
        <module name="MethodName"/>
        <module name="PackageName"/>
        <module name="ParameterName"/>
        <module name="StaticVariableName"/>
        <module name="TypeName"/>

        <!-- Checks for imports                              -->
        <!-- See http://checkstyle.sf.net/config_import.html -->
        <module name="AvoidStarImport">
          <property name="excludes" value="com.fasterxml.jackson.annotation" />
          <property name="excludes" value="com.fasterxml.jackson.databind.annotation" />
        </module>
        <module name="IllegalImport"/> <!-- defaults to sun.* packages -->
        <module name="RedundantImport"/>
        <module name="UnusedImports"/>



        <module name="MethodLength"/>
        <module name="ParameterNumber"/>


        <!-- Checks for whitespace                               -->
        <!-- See http://checkstyle.sf.net/config_whitespace.html -->
        <module name="EmptyForIteratorPad"/>
        <module name="GenericWhitespace"/>
        <module name="MethodParamPad"/>
        <module name="NoWhitespaceAfter">
          <!-- Remove ARRAY_INIT to allow array initialzation like "new int[] { 1, 2, 3 }" -->
          <property name="tokens" value="INC, DEC, UNARY_MINUS, UNARY_PLUS, BNOT, LNOT, DOT, ARRAY_DECLARATOR, INDEX_OP"/> <!-- AT? -->
        </module>
        <module name="NoWhitespaceBefore"/>
        <module name="OperatorWrap"/>
        <module name="ParenPad"/>
        <module name="TypecastParenPad"/>
        <module name="WhitespaceAfter"/>
        <module name="WhitespaceAround"/>

        <!-- Modifier Checks                                    -->
        <!-- See http://checkstyle.sf.net/config_modifiers.html -->
        <module name="ModifierOrder"/>
        <module name="RedundantModifier">
          <property name="tokens" value="ENUM_DEF"/>
        </module>
        <module name="InterfaceMemberImpliedModifier">
          <property name="violateImpliedPublicMethod" value="false"/>
          <property name="violateImpliedAbstractMethod" value="false"/>
          <property name="violateImpliedPublicNested" value="false"/>
          <property name="violateImpliedStaticNested" value="false"/>
        </module>

        <!-- Checks for blocks. You know, those {}'s         -->
        <!-- See http://checkstyle.sf.net/config_blocks.html -->
        <module name="AvoidNestedBlocks">
          <property name="allowInSwitchCase" value="true"/>
        </module>
        <module name="EmptyBlock">
          <property name="option" value="text"/>
        </module>
        <module name="LeftCurly">
          <property name="tokens" value="ANNOTATION_DEF,CLASS_DEF,ENUM_DEF,INTERFACE_DEF,LITERAL_CATCH,LITERAL_DO,LITERAL_ELSE,LITERAL_FINALLY,LITERAL_IF,LITERAL_FOR,LITERAL_SWITCH,LITERAL_SYNCHRONIZED,LITERAL_TRY,LITERAL_WHILE"/>
        </module>
        <module name="NeedBraces"/>
        <module name="RightCurly"/>


        <!-- Checks for common coding problems               -->
        <!-- See http://checkstyle.sf.net/config_coding.html -->
        <!-- <module name="AvoidInlineConditionals"/> -->
        <module name="EmptyStatement"/>
        <module name="EqualsHashCode"/>
        <module name="HiddenField">
          <property name="ignoreConstructorParameter" value="true"/>
          <property name="ignoreSetter" value="true"/>
          <property name="severity" value="warning"/>
        </module>
        <module name="IllegalInstantiation"/>
        <module name="InnerAssignment"/>
        <module name="MagicNumber">
          <property name="severity" value="warning"/>
        </module>
        <module name="MissingSwitchDefault"/>
        <module name="SimplifyBooleanExpression"/>
        <module name="SimplifyBooleanReturn"/>

        <!-- Checks for class design                         -->
        <!-- See http://checkstyle.sf.net/config_design.html -->
        <!-- <module name="DesignForExtension"/> -->
        <module name="FinalClass"/>
        <module name="HideUtilityClassConstructor"/>
        <module name="InterfaceIsType"/>
        <module name="VisibilityModifier"/>


        <!-- Miscellaneous other checks.                   -->
        <!-- See http://checkstyle.sf.net/config_misc.html -->
        <module name="ArrayTypeStyle"/>
        <module name="FinalParameters"/>
        <module name="TodoComment">
          <property name="format" value="TODO"/>
          <property name="severity" value="info"/>
        </module>
        <module name="UpperEll"/>

        <!-- Enable suppression comments -->
        <module name="SuppressionCommentFilter">
          <property name="offCommentFormat" value="CHECKSTYLE IGNORE\s+(\S+)"/>
          <property name="onCommentFormat" value="CHECKSTYLE END IGNORE\s+(\S+)"/>
          <property name="checkFormat" value="$1"/>
        </module>
        <module name="SuppressWithNearbyCommentFilter">
          <!-- Syntax is "SUPPRESS CHECKSTYLE name" -->
          <property name="commentFormat" value="SUPPRESS CHECKSTYLE (\w+)"/>
          <property name="checkFormat" value="$1"/>
          <property name="influenceFormat" value="1"/>
        </module>

    </module>

</module>
