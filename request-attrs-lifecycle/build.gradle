apply plugin: 'netflix.jvm-library'
apply plugin: 'netflix.spring-boot-netflix-library'

dependencyRecommendations {
    mavenBom module: 'com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations:latest.release'
}

dependencies {
    api project(':request-attrs-api')
    api project(':request-attrs-core')
    api project(':request-attrs-base-server')

    compileOnly "com.google.inject:guice"

    compileOnly 'org.springframework.boot:spring-boot'
    compileOnly 'org.springframework.boot:spring-boot-autoconfigure'

    testImplementation "com.netflix.governator:governator-core"
    testImplementation 'com.netflix.governator:governator-test-junit'

    testImplementation 'com.fasterxml.jackson.datatype:jackson-datatype-jdk8' // support for Optional

    testImplementation 'org.springframework.boot:spring-boot'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.boot:spring-boot-autoconfigure'

    testCompileOnly 'com.netflix.spring:spring-boot-netflix-starter-library'
    testRuntimeOnly 'org.junit.vintage:junit-vintage-engine'
}
tasks.withType(Test).configureEach { 
    useJUnitPlatform()
}
