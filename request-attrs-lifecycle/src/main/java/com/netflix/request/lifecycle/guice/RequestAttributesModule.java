package com.netflix.request.lifecycle.guice;

import com.google.inject.AbstractModule;

import com.netflix.request.RequestAttributesManager;
import com.netflix.request.VisitContextManager;
import com.netflix.request.baseserver.RequestAttributesManagerBaseServer;
import com.netflix.request.core.StandardVisitContextManager;

/**
 * <PERSON><PERSON>ce <PERSON> for initializing the library.
 * Any library or application that directly depends upon the library
 * should install an instance of this class before using any resource
 * provided by this library.
 */
public final class RequestAttributesModule extends AbstractModule {

    @Override
    protected void configure() {
        bind(RequestAttributesManager.class).to(RequestAttributesManagerBaseServer.class);
        bind(VisitContextManager.class).to(StandardVisitContextManager.class);
    }

    /*
     * Ensure the module is loaded only once
     */

    @Override
    public boolean equals(final Object obj) {
        return obj != null && getClass().equals(obj.getClass());
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

}
