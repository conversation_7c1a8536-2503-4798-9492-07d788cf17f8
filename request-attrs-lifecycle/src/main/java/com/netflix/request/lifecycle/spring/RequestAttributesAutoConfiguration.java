package com.netflix.request.lifecycle.spring;

import com.netflix.request.RequestAttributesManager;
import com.netflix.request.VisitContextManager;
import com.netflix.request.baseserver.RequestAttributesManagerBaseServer;
import com.netflix.request.core.StandardVisitContextManager;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Spring auto-configuration for the library.
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(name = "request_attributes.base_server.enabled", matchIfMissing = true)
@ConditionalOnClass(RequestAttributesManagerBaseServer.class)
public class RequestAttributesAutoConfiguration {

    @Bean
    RequestAttributesManager request_attrs_lifecycleRequestAttributesManager() {
        return new RequestAttributesManagerBaseServer();
    }

    @Bean
    VisitContextManager request_attrs_lifecycleStandardVisitContextManager(RequestAttributesManager reqAttrManager) {
        return new StandardVisitContextManager(reqAttrManager);
    }
}
