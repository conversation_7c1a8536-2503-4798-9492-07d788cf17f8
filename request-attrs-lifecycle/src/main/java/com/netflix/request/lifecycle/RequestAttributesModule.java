package com.netflix.request.lifecycle;

import com.google.inject.AbstractModule;

/**
 * <PERSON><PERSON><PERSON> for initializing the library.
 * Any library or application that directly depends upon the library
 * should install an instance of this class before using any resource
 * provided by this library.
 *
 * @deprecated use {@link com.netflix.request.lifecycle.guice.RequestAttributesModule} instead
 */
@Deprecated
public final class RequestAttributesModule extends AbstractModule {

    @Override
    protected void configure() {
        install(new com.netflix.request.lifecycle.guice.RequestAttributesModule());
    }

    /*
     * Ensure the module is loaded only once
     */

    @Override
    public boolean equals(final Object obj) {
        return obj != null && getClass().equals(obj.getClass());
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

}
