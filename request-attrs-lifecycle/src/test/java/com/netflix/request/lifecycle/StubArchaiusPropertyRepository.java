package com.netflix.request.lifecycle;

import com.netflix.archaius.DefaultPropertyFactory;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.archaius.config.DefaultSettableConfig;

import java.lang.reflect.Type;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

// Copied from corp:ocp-ocp lib/src/test/java/com/netflix/streaming/utils/properties/TestPropertyRepository.java

public class StubArchaiusPropertyRepository implements PropertyRepository {

    private final DefaultPropertyFactory propertyFactory;
    private final DefaultSettableConfig settableConfig;

    public StubArchaiusPropertyRepository() {
        this(Collections.emptyMap());
    }

    public <T> StubArchaiusPropertyRepository(Map<String, T> config) {
        this.settableConfig = new DefaultSettableConfig("settable");
        config.forEach(settableConfig::setProperty);
        this.propertyFactory = DefaultPropertyFactory.from(settableConfig);
    }

    public <T> void setProperty(String name, T value) {
        Objects.requireNonNull(
                settableConfig, "StubArchaiusPropertyRepository must be created with a settable config to update");
        settableConfig.setProperty(name, value);
    }

    @Override
    public <T> Property<T> get(String key, Class<T> type) {
        return propertyFactory.get(key, type);
    }

    @Override
    public <T> Property<T> get(String key, Type type) {
        return propertyFactory.get(key, type);
    }

}
