package com.netflix.request.lifecycle;

import com.netflix.request.RequestAttributesManager;
import com.netflix.request.baseserver.RequestAttributesManagerBaseServer;

import org.junit.Assert;
import org.junit.Test;

import javax.inject.Inject;

public abstract class RequestAttributesModuleTest {

    @Inject
    private RequestAttributesManager manager;

    @Test
    public void testDI() {
        Assert.assertTrue(manager instanceof RequestAttributesManagerBaseServer);
    }
}
