package com.netflix.request.lifecycle.spring;

import com.netflix.request.lifecycle.RequestAttributesModuleTest;
import com.netflix.request.lifecycle.StubArchaiusPropertyRepository;
import com.netflix.spectator.api.NoopRegistry;
import org.junit.runner.RunWith;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@TestPropertySource("classpath:application-test.properties")
@EnableAutoConfiguration
@ContextConfiguration(classes = { NoopRegistry.class, StubArchaiusPropertyRepository.class })
public class RequestAttributesModuleTestSpring extends RequestAttributesModuleTest {

}
