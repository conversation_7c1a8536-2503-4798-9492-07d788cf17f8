{"inputs": {"teamEmail": "<EMAIL>", "repoOwner": "cpiec", "repoName": "request-attrs", "repoOwnerType": "project", "cloudProvider": "aws", "projectType": "multiproject-library", "generatorTasks": ["generate", "jenkins"], "projectName": "request-attrs", "projectClassName": "RequestAttributes", "packageName": "com.netflix.request", "testFramework": "junit", "deployableArtifactName": "", "ciProjectType": "java-library", "jenkinsServer": "platform", "prodAccount": "prod", "testAccount": "test"}, "generator_user": "slanning", "ci_generator_version": "3.1.7", "project_resources": {"jenkins": {"jobs": [{"name": "CPIEC-request-attrs-build-master", "friendlyName": "Build Master", "source": "build-master.xml", "url": "https://platform.builds.test.netflix.net/job/CPIEC-request-attrs-build-master", "default": true}, {"name": "CPIEC-request-attrs-build-pull-request", "friendlyName": "Build Pull Request", "source": "build-pull-request.xml", "url": "https://platform.builds.test.netflix.net/job/CPIEC-request-attrs-build-pull-request"}, {"name": "CPIEC-request-attrs-release", "friendlyName": "Release", "source": "release.xml", "url": "https://platform.builds.test.netflix.net/job/CPIEC-request-attrs-release"}, {"name": "CPIEC-request-attrs-update-dependencies-lock", "friendlyName": "Update Dependencies", "source": "update-dependencies-lock.xml", "url": "https://platform.builds.test.netflix.net/job/CPIEC-request-attrs-update-dependencies-lock"}], "server": "platform"}, "CI Pipeline Flowchart": "https://stash.corp.netflix.com/snippets/raw/551dc37898ae4a519402ce7527bbe6b1/Generated_ci_pipeline.txt"}}