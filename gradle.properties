# The common build properties file for libraries.
# Properties defined here will override ALL other definitions.

module.owner=<EMAIL>

# Email will be sent to the (comma separated) module.email when this library is released.
module.email=<EMAIL>

# The sources to be excluded for compile, use ant-style patterns
#source.excludes=com.netflix.interest.engine.bogus/**/*.java

# Resources to exlude from being distributed in the JAR file of the release
#resource.excludes=blah_blah.doc

# Test files to be excluded from the .jar. Use an Ant-style pattern to match package/package/file.
#excludeTest=**

# The sources to be excluded for junit/testng, use ant-style patterns
#test.excludes=**/*$*.class,**/CustomServletTester.class,**/TestResource.class
#test.maxmemory=1024m
#test.jvmargs=-javaagent:build/lib/test/mockit-0.999.8.jar

# Get as many warning messages out of the compiler as possible
javac.lint.option=all

# Ditto for findbugs
findbugs.reportLevel=low

javadoc.overview=./docs/overview.html
javadoc.noqualifier=java.lang:java.util:com.netflix.type
javadoc.access=protected

# release will be ensured with the use of `attributes` in the `build.gradle` file
# build tools will be implementing syntactic sugar so that version constraints can be expressed solely with version specs -- https://jira.netflix.com/browse/NEBULA-952
systemProp.netflix.grpc-plugins_netflix-grpc-plugins.version=7.+
