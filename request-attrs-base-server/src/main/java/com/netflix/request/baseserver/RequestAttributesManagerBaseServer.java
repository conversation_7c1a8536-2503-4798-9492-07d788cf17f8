package com.netflix.request.baseserver;

import static com.netflix.microcontext.access.server.ContextUtils.fromDefault;

import com.google.protobuf.util.Timestamps;
import com.netflix.geoclient.GeoData;
import com.netflix.geoclient.context.GeoRequestContextManager;
import com.netflix.lang.BindingContexts;
import com.netflix.lang.RequestVariable;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.ContextUtils;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.migration.RequestTimeMigration;
import com.netflix.request.RequestAttributes;
import com.netflix.request.core.AbstractRequestAttributesManager;
import com.netflix.request.core.RequestAttributesPOJO;
import com.netflix.request.core.UiFlavors;
import com.netflix.request.protogen.UiFlavor;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.server.context.RequestContext;
import com.netflix.type.Visitor;

import javax.annotation.Nullable;
import javax.inject.Inject;
import javax.inject.Singleton;

import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * Implementation of the {@code RequestAttributesManager} interface that uses
 * the Netflix base-server supported {@code CurrentRequestContext}
 * and {@code CurrentVisitor}.
 * The {@code RequestAttributes} will be constructed lazily on first-fetch
 * and the value is cached in a request-scoped variable.
 * Subsequent fetches will use the cache, and so should be very fast.
 */
@Singleton
public class RequestAttributesManagerBaseServer extends AbstractRequestAttributesManager {

    /**
     * Key for storing the time-travel time in the RequestContext.
     * <p>
     * <b>Note</b>
     * It is important that this matches the value used in the CP-1 {@code NFReqContextImpl} class.
     */
    static final String TIME_KEY = "RequestTimestamp";

    /**
     * Key for storing the "UIFlavor" flag in the RequestContext.
     *
     * @see <a href='http://go.netflix.com/clientlist'>Client Applications spreadsheet</a>
     */
    static final String UI_FLAVOR_CONTEXT_KEY = "UIFlavor";

    /**
     * Key for storing the "isArtificial" flag in the RequestContext.
     * <p>
     * <b>Note</b>
     * It is important that this matches the value used in the CP-1 {@code NFReqContextImpl} class.
     */
    static final String IS_ARTIFICIAL_KEY = "IsArtificial";

    /**
     * Key for storing the "isVPNProxy" flag in the RequestContext.
     * <p>
     * <b>Note</b>
     * It is important that this matches the value used by VMS.
     */
    static final String IS_VPN_VMS_PROXY_KEY = "VMS_VPN";

    /**
     * Key for storing the Track-ID in the RequestContext.
     */
    static final String TRACK_ID_KEY = "trkid";

    /**
     * Key for storing the Content Preview ID in the RequestContext.
     * This should really be from com.netflix.beehive.contentpreview.lookup.util.Constants.
     */
    static final String CONTENT_PREVIEW_ID_CONTEXT_KEY = "ContentPreviewAccountId";

    private final RequestVariable<RequestAttributes> currentRequestAttributes = new RequestVariable<RequestAttributes>() {
        @Override
        protected RequestAttributes initialValue() {
            return makeCurrentRequestAttributes();
        }
    };

    @Inject
    public RequestAttributesManagerBaseServer() {
        super();
    }

    /*
     * Accessors
     */

    /**
     * Back-door for setting the current RequestAttributes.
     * Use at your own risk!
     */
    /* package */void setCurrentRequestAttributes(final RequestAttributes newReqAttrs) {
        currentRequestAttributes.set(newReqAttrs);
    }

    /*
     * RequestAttributesManager protocol
     */

    @Override
    public RequestAttributes.Builder newRequestAttributesBuilder() {
        return RequestAttributesPOJO.builder();
    }

    @Override
    public RequestAttributes getCurrentRequestAttributes() {
        if (!BindingContexts.isInContext()) {
            throw new IllegalStateException("No current Binding Context");
        }
        return currentRequestAttributes.get();
    }

    @Override
    public boolean hasCurrentRequestAttributes() {
        return BindingContexts.isInContext();
    }

    /**
     * Creates a new {@code RequestAttributes} that is a snapshot of the current request context.
     * This will throw RuntimeExceptions if values in the RequestContext cannot be parsed.
     */
    static RequestAttributes makeCurrentRequestAttributes() {
        return makeRequestAttributes(CurrentRequestContext.get(),
                                     CurrentVisitor.get());
    }

    /**
     * Creates a new {@code RequestAttributes} that is a snapshot of the given request context and visitor.
     * @throws RuntimeException if values in the RequestContext cannot be parsed.
     */
    static RequestAttributes makeRequestAttributes(@Nullable final RequestContext ctxt, @Nullable final Visitor visitor) {
        final RequestAttributes.Builder builder = RequestAttributesPOJO.builder();
        if (ctxt != null) {
            final Optional<Microcontext> microcontext = CurrentMicrocontext.requestContext(ctxt)
                .map(ContextUtils::fromProto);
            builder.withRequestId(ctxt.getRequestId());
            builder.withTrackId(ctxt.get(TRACK_ID_KEY));
            builder.withDeviceType(ctxt.getDeviceType());
            builder.withDeviceId(ctxt.getDeviceId());
            try {
                final String rawUiFlavor = ctxt.getContext(UI_FLAVOR_CONTEXT_KEY);
                if (rawUiFlavor != null) {
                    UiFlavors.fromName(rawUiFlavor)
                             .ifPresent(builder::withUIFlavor);
                }
            } catch (RuntimeException e) {
                throw e;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            builder.withCountry(ctxt.getCountry());

            // fetch request time via the migration utility which will check microcontext first
            // and default back to request context if not present
            RequestTimeMigration.getRequestTime(ctxt, microcontext.orElse(fromDefault()))
                    .ifPresent(ts -> builder.withTime(Timestamps.toMillis(ts)));

            builder.withGMTOffset(ctxt.getGMTOffset());
            final String tz = ctxt.getTimezone();
            builder.withTimeZone(tz == null ? null : TimeZone.getTimeZone(tz));
            builder.withLocales(Optional.ofNullable(ctxt.getLocaleList()).orElse("").split(","));
            try {
                builder.withContentPreviewId(ctxt.getContext(CONTENT_PREVIEW_ID_CONTEXT_KEY));
            } catch (RuntimeException e) {
                throw e;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            builder.withExtraAttributes(ExtraAttributesSupport.getExtraAttributes(ctxt));
            final String isArtificial = ctxt.get(IS_ARTIFICIAL_KEY);
            builder.withIsArtificial((isArtificial == null) ? false : Boolean.parseBoolean(isArtificial));
            try {
                builder.withIsVPNProxy(isVpn(ctxt));
            } catch (RuntimeException e) {
                throw e;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        builder.withVisitor(visitor);
        return builder.build();
    }

    static boolean isVpn(final RequestContext ctxt) throws ContextSerializationException {
        final String vmsVpnProxy = ctxt.getContext(IS_VPN_VMS_PROXY_KEY);
        if (vmsVpnProxy != null) {
            return "true".equals(vmsVpnProxy);
        }
        GeoData geoData = GeoRequestContextManager.instance().readFromGeoRequestContext(ctxt);
        return geoData != null && geoData.isBlockedProxy();
    }


    @Override
    public <T> T callWithRequestAttributes(final RequestAttributes attrs, final Callable<T> callable) throws Exception {
        final RequestContext ctxt = makeRequestContext(attrs);
        return BindingContexts.callWithNewContext(() -> {
                                                      CurrentRequestContext.set(ctxt);
                                                      CurrentVisitor.set(attrs.getVisitor());
                                                      return callable.call();
                                                  });
    }

    @Override
    public void runWithRequestAttributes(final RequestAttributes attrs, final Runnable runnable) {
        final RequestContext ctxt = makeRequestContext(attrs);
        BindingContexts.runWithNewContext(() -> {
                                              CurrentRequestContext.set(ctxt);
                                              CurrentVisitor.set(attrs.getVisitor());
                                              runnable.run();
                                          });
    }

    @Override
    public <T> T getWithRequestAttributes(final RequestAttributes attrs, final Supplier<T> supplier) {
        final RequestContext ctxt = makeRequestContext(attrs);
        try {
            return BindingContexts.callWithNewContext(() -> {
                                                          CurrentRequestContext.set(ctxt);
                                                          CurrentVisitor.set(attrs.getVisitor());
                                                          return supplier.get();
                                                      });
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Creates a new {@code RequestContext} that contains data from the given {@code RequestAttributes}.
     */
    static RequestContext makeRequestContext(final RequestAttributes attrs) {
        RequestContext ctxt;
        // Try to preserve calling info (Salp data etc) unless the attrs specifies a request-id that differs from the current one
        final RequestContext currentCtxt = BindingContexts.isInContext() ? CurrentRequestContext.get() : null;
        if (currentCtxt != null && Objects.equals(attrs.getRequestId(), currentCtxt.getRequestId())) {
            // Request-id is the same, so clone the current context to preserve calling info
            ctxt = currentCtxt.clone();
        } else {
            // Start with a totally empty context
            ctxt = makeEmptyRequestContext();
            ctxt.setRequestId(attrs.getRequestId());
        }
        // Copy non-request-id data from the RequestAttributes into the RequestContext
        if (attrs.getTrackId() != null) {
            // If track-id is defined, use it. Otherwise leave well enough alone.
            ctxt.put(TRACK_ID_KEY, attrs.getTrackId());
        }
        ctxt.setDeviceType(attrs.getDeviceType());
        ctxt.setDeviceId(attrs.getDeviceId());
        final UiFlavor flavor = attrs.getUIFlavor();
        if (flavor != null) {
            ctxt.addContext(UI_FLAVOR_CONTEXT_KEY, flavor.name());
        } else {
            ctxt.removeContext(UI_FLAVOR_CONTEXT_KEY);
        }
        ctxt.setCountry(attrs.getCountry());
        final Long time = attrs.getTime();
        // TODO put back into MC as well?
        ctxt.put(TIME_KEY, (time == null) ? null : time.toString());
        if (attrs.getGMTOffset() != null) {
            ctxt.setGMTOffset(attrs.getGMTOffset());
        } else if (attrs.getTimeZone() != null) {
            ctxt.setGMTOffset(attrs.getTimeZone().getRawOffset());
        } else if (ctxt.getGMTOffset() == null) {
            // OK, already null
        } else {
            throw new UnsupportedOperationException("Cannot clear the GMT offset of a RequestContext");
        }
        final TimeZone tz = attrs.getTimeZone();
        ctxt.setTimezone((tz == null) ? null : tz.getID());
        ctxt.setLocaleList(String.join(",", attrs.getLocales()));
        if (attrs.getContentPreviewId() != null) {
            ctxt.addContext(CONTENT_PREVIEW_ID_CONTEXT_KEY, attrs.getContentPreviewId());
        } else {
            ctxt.removeContext(CONTENT_PREVIEW_ID_CONTEXT_KEY);
        }
        ExtraAttributesSupport.putExtraAttributes(ctxt, attrs.getExtraAttributes());
        if (attrs.isArtificial()) {
            ctxt.put(IS_ARTIFICIAL_KEY, "true");
        } else {
            ctxt.remove(IS_ARTIFICIAL_KEY);
        }
        // If the context is simply removed, callers would fall back to read the GeoData underneath,
        // which may have a different value for VPN. Push the value into the context, 'true' or 'false'.
        ctxt.addContext(IS_VPN_VMS_PROXY_KEY, Boolean.toString(attrs.isVPNProxy()));

        // All done
        return ctxt;
    }

    private static RequestContext makeEmptyRequestContext() {
        // Ugly trick to get a new, empty RequestContext
        try {
            return BindingContexts.callWithNewContext(() -> CurrentRequestContext.get().clone());
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            // Shouldn't happen
            throw new RuntimeException(e);
        }
    }

} // RequestAttributesManagerBaseServer
