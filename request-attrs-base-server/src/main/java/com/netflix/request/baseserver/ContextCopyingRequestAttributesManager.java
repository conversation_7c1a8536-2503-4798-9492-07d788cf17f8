package com.netflix.request.baseserver;

import com.netflix.lang.BindingContexts;
import com.netflix.lang.RequestVariable;
import com.netflix.lang.ThreadContext;
import com.netflix.request.RequestAttributes;
import com.netflix.request.core.AbstractRequestAttributesManager;
import com.netflix.request.core.RequestAttributesPOJO;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.server.context.RequestContext;

import javax.inject.Singleton;
import java.util.concurrent.Callable;
import java.util.function.Supplier;

import static com.netflix.request.baseserver.RequestAttributesManagerBaseServer.makeRequestContext;

@Singleton
public class ContextCopyingRequestAttributesManager extends AbstractRequestAttributesManager {

  private final RequestVariable<RequestAttributes> currentRequestAttributes =
      new RequestVariable<RequestAttributes>() {
        @Override
        protected RequestAttributes initialValue() {
          return RequestAttributesManagerBaseServer.makeCurrentRequestAttributes();
        }
      };

  @Override
  public RequestAttributes.Builder newRequestAttributesBuilder() {
    return RequestAttributesPOJO.builder();
  }

  @Override
  public RequestAttributes getCurrentRequestAttributes() {
    return currentRequestAttributes.get();
  }

  @Override
  public <T> T callWithRequestAttributes(final RequestAttributes attrs, final Callable<T> callable)
      throws Exception {
    final RequestContext ctxt = makeRequestContext(attrs);
    final RequestAttributes captured =
        BindingContexts.isInContext() ? getCurrentRequestAttributes() : null;
    ThreadContext threadContext = ThreadContext.capture();
    try {
      return BindingContexts.callWithContext(
          () -> {
            currentRequestAttributes.set(attrs);
            CurrentRequestContext.set(ctxt);
            CurrentVisitor.set(attrs.getVisitor());
            return callable.call();
          },
          threadContext);
    } finally {
      currentRequestAttributes.set(captured);
    }
  }

  @Override
  public void runWithRequestAttributes(RequestAttributes attrs, Runnable runnable) {
    final RequestContext ctxt = makeRequestContext(attrs);
    final RequestAttributes captured =
        BindingContexts.isInContext() ? getCurrentRequestAttributes() : null;
    ThreadContext threadContext = ThreadContext.capture();
    try {
      BindingContexts.runWithContext(
          () -> {
            currentRequestAttributes.set(attrs);
            CurrentRequestContext.set(ctxt);
            CurrentVisitor.set(attrs.getVisitor());
            runnable.run();
          },
          threadContext);
    } finally {
      currentRequestAttributes.set(captured);
    }
  }

  @Override
  public <T> T getWithRequestAttributes(RequestAttributes attrs, Supplier<T> supplier) {
    final RequestContext ctxt = makeRequestContext(attrs);
    final RequestAttributes captured =
        BindingContexts.isInContext() ? getCurrentRequestAttributes() : null;
    ThreadContext threadContext = ThreadContext.capture();
    try {
      return BindingContexts.callWithContext(
          () -> {
            currentRequestAttributes.set(attrs);
            CurrentRequestContext.set(ctxt);
            CurrentVisitor.set(attrs.getVisitor());
            return supplier.get();
          },
          threadContext);
    } catch (RuntimeException e) {
      throw e;
    } catch (Exception e) {
      throw new RuntimeException(e);
    } finally {
      currentRequestAttributes.set(captured);
    }
  }
}
