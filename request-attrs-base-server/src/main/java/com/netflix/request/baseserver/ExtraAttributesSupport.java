package com.netflix.request.baseserver;

import com.netflix.abzuulcontext.ABZuulContextFactory;
import com.netflix.abzuulcontext.protogen.ABZuulContextData;
import com.netflix.server.context.RequestContext;

import javax.annotation.Nullable;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Static holder of utility methods for dealing with the ABZuulContext.
 * <p>
 * <b>Note:</b>
 * This implementation supports the {@code ABZuulContext} extra parameters,
 * using names of the form "zuul_<i>group</i>_<i>field</i>" where the <i>group</i>
 * is one of "tvui", "samurai", "webui", "ios", or "signupwizard".
 * Any other extra parameters are ignored.
 */
final class ExtraAttributesSupport {

    /*
     * Lifecycle
     */

    /**
     * Creates a new <code>ExtraAttributesSupport</code> instance.
     */
    private ExtraAttributesSupport() { }

    /*
     * Support for ABZuulContext
     */

    /**
     * Extracts the map of extra attributes from a {@code RequestContext}.
     */
    static Map<String, String> getExtraAttributes(final RequestContext ctxt) {
        try {
            final String base64ZuulContext = ctxt.getContext(ABZuulContextFactory.getAbZuulContextKey());
            if (base64ZuulContext == null || base64ZuulContext.equals("")) {
                return Collections.emptyMap();
            }
            final ABZuulContextData.ABZuulContext zc = ABZuulContextFactory.deserializeABZuulContextData(base64ZuulContext);
            final Map<String, String> map = new HashMap<>();
            if (zc.hasTvUIFields()) {
                put(map, "zuul_tvui_uiVersion", zc.getTvUIFields().getUiVersion());
                put(map, "zuul_tvui_nrdAppVersion", zc.getTvUIFields().getNrdAppVersion());
                put(map, "zuul_tvui_currentUIType", zc.getTvUIFields().getCurrentUIType());
            }
            if (zc.hasSamuraiFields()) {
                put(map, "zuul_samurai_appVersion", zc.getSamuraiFields().getAppVersion());
                put(map, "zuul_samurai_currentUIType", zc.getSamuraiFields().getCurrentUIType());
                put(map, "zuul_samurai_androidApi", zc.getSamuraiFields().getAndroidApi());
                put(map, "zuul_samurai_deviceFormFactor", zc.getSamuraiFields().getDeviceFormFactor());
            }
            if (zc.hasWebUIFields()) {
                put(map, "zuul_webui_browserName", zc.getWebUIFields().getBrowserName());
                put(map, "zuul_webui_browserVersion", zc.getWebUIFields().getBrowserVersion());
                put(map, "zuul_webui_osName", zc.getWebUIFields().getOsName());
                put(map, "zuul_webui_osVersion", zc.getWebUIFields().getOsVersion());
                put(map, "zuul_webui_uiVersion", zc.getWebUIFields().getUiVersion());
                put(map, "zuul_webui_currentUIType", zc.getWebUIFields().getCurrentUIType());
            }
            if (zc.hasIOSFields()) {
                put(map, "zuul_ios_idiom", zc.getIOSFields().getIdiom());
                put(map, "zuul_ios_appVersion", zc.getIOSFields().getAppVersion());
                put(map, "zuul_ios_currentUIType", zc.getIOSFields().getCurrentUIType());
                put(map, "zuul_ios_iosVersion", zc.getIOSFields().getIosVersion());
            }
            if (zc.hasSignupWizardFields()) {
                put(map, "zuul_signupwizard_uiVersion", zc.getSignupWizardFields().getUiVersion());
                put(map, "zuul_signupwizard_nrdAppVersion", zc.getSignupWizardFields().getNrdAppVersion());
                put(map, "zuul_signupwizard_currentUIType", zc.getSignupWizardFields().getCurrentUIType());
            }
            return map;
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static void put(final Map<String, String> map, final String key, @Nullable final String value) {
        // Required because of the way that Protobuf handles default values
        if (value == null || value.equals("")) {
            map.remove(key);
        } else {
            map.put(key, value);
        }
    }

    /**
     * Stores a map of extra attributes into a {@code RequestContext}.
     */
    @Nullable
    static void putExtraAttributes(final RequestContext ctxt, final Map<String, String> attrs) {
        if (attrs.isEmpty()) {
            return;
        }

        boolean tvUISet = false;
        ABZuulContextData.ABZuulContext.TVUIFields.Builder tvUIBuilder = ABZuulContextData.ABZuulContext.TVUIFields.newBuilder();

        boolean samuraiSet = false;
        ABZuulContextData.ABZuulContext.SamuraiFields.Builder samuraiBuilder = ABZuulContextData.ABZuulContext.SamuraiFields.newBuilder();

        boolean webUISet = false;
        ABZuulContextData.ABZuulContext.WebUIFields.Builder webUIBuilder = ABZuulContextData.ABZuulContext.WebUIFields.newBuilder();

        boolean iosSet = false;
        ABZuulContextData.ABZuulContext.IOSFields.Builder iosBuilder = ABZuulContextData.ABZuulContext.IOSFields.newBuilder();

        boolean signupWizardSet = false;
        ABZuulContextData.ABZuulContext.SignupWizardFields.Builder signupWizardBuilder = ABZuulContextData.ABZuulContext.SignupWizardFields.newBuilder();

        for (Map.Entry<String, String> e : attrs.entrySet()) {
            final String key = e.getKey();
            final String value = e.getValue();
            if (value == null || value.equals("")) {
                continue;
            }
            switch (key) {

            case "zuul_tvui_uiVersion":
                tvUISet = true;
                tvUIBuilder.setUiVersion(value);
                break;
            case "zuul_tvui_nrdAppVersion":
                tvUISet = true;
                tvUIBuilder.setNrdAppVersion(value);
                break;
            case "zuul_tvui_currentUIType":
                tvUISet = true;
                tvUIBuilder.setCurrentUIType(value);
                break;

            case "zuul_samurai_appVersion":
                samuraiSet = true;
                samuraiBuilder.setAppVersion(value);
                break;
            case "zuul_samurai_currentUIType":
                samuraiSet = true;
                samuraiBuilder.setCurrentUIType(value);
                break;
            case "zuul_samurai_androidApi":
                samuraiSet = true;
                samuraiBuilder.setAndroidApi(value);
                break;
            case "zuul_samurai_deviceFormFactor":
                samuraiSet = true;
                samuraiBuilder.setDeviceFormFactor(value);
                break;

            case "zuul_webui_browserName":
                webUISet = true;
                webUIBuilder.setBrowserName(value);
                break;
            case "zuul_webui_browserVersion":
                webUISet = true;
                webUIBuilder.setBrowserVersion(value);
                break;
            case "zuul_webui_osName":
                webUISet = true;
                webUIBuilder.setOsName(value);
                break;
            case "zuul_webui_osVersion":
                webUISet = true;
                webUIBuilder.setOsVersion(value);
                break;
            case "zuul_webui_uiVersion":
                webUISet = true;
                webUIBuilder.setUiVersion(value);
                break;
            case "zuul_webui_currentUIType":
                webUISet = true;
                webUIBuilder.setCurrentUIType(value);
                break;

            case "zuul_ios_idiom":
                iosSet = true;
                iosBuilder.setIdiom(value);
                break;
            case "zuul_ios_appVersion":
                iosSet = true;
                iosBuilder.setAppVersion(value);
                break;
            case "zuul_ios_currentUIType":
                iosSet = true;
                iosBuilder.setCurrentUIType(value);
                break;
            case "zuul_ios_iosVersion":
                iosSet = true;
                iosBuilder.setIosVersion(value);
                break;

            case "zuul_signupwizard_uiVersion":
                signupWizardSet = true;
                signupWizardBuilder.setUiVersion(value);
                break;
            case "zuul_signupwizard_nrdAppVersion":
                signupWizardSet = true;
                signupWizardBuilder.setNrdAppVersion(value);
                break;
            case "zuul_signupwizard_currentUIType":
                signupWizardSet = true;
                signupWizardBuilder.setCurrentUIType(value);
                break;

            default:
                // ignore
                break;
            }
        }
        if (!(tvUISet || samuraiSet || webUISet || iosSet || signupWizardSet)) {
            return;
        }

        final ABZuulContextData.ABZuulContext.Builder builder = ABZuulContextData.ABZuulContext.newBuilder();
        if (tvUISet) {
            builder.setTvUIFields(tvUIBuilder.build());
        }
        if (samuraiSet) {
            builder.setSamuraiFields(samuraiBuilder.build());
        }
        if (webUISet) {
            builder.setWebUIFields(webUIBuilder.build());
        }
        if (iosSet) {
            builder.setIOSFields(iosBuilder.build());
        }
        if (signupWizardSet) {
            builder.setSignupWizardFields(signupWizardBuilder.build());
        }
        ctxt.addContext(ABZuulContextFactory.getAbZuulContextKey(),
                        ABZuulContextFactory.createB64ABZuulContextFromBean(builder.build()));
    }

} // ExtraAttributesSupport
