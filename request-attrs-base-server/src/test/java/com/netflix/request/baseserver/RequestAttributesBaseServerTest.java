package com.netflix.request.baseserver;

import com.netflix.geoclient.GeoData;
import com.netflix.geoclient.GeoDataImpl;
import com.netflix.geoclient.context.GeoRequestContextManager;
import com.netflix.lang.BindingContexts;
import com.netflix.request.RequestAttributes;
import com.netflix.request.RequestAttributesManager;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.server.context.RequestContext;
import com.netflix.type.NFCountry;
import com.netflix.type.SimpleDeviceType;
import com.netflix.type.SimpleVisitor;

import org.junit.Assert;
import org.junit.Test;

import java.util.Collections;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

public class RequestAttributesBaseServerTest {

    private final RequestAttributesManager manager = new RequestAttributesManagerBaseServer();

    @Test
    public void testAttrsFromContext() {
        BindingContexts.runWithNewContext(() -> {
                                              final RequestContext ctxt = CurrentRequestContext.get();
                                              ctxt.setRequestId("Some request ID");
                                              ctxt.setDeviceType(new SimpleDeviceType(12345));
                                              ctxt.setDeviceId("Some device ID");
                                              ctxt.setCountry(NFCountry.ES);
                                              CurrentVisitor.set(new SimpleVisitor(5845828167678734L));
                                              ctxt.put(RequestAttributesManagerBaseServer.TIME_KEY, "1519708238000"); // 2018-02-27T05:10:38 GMT
                                              ctxt.setLocaleList("es,en");
                                              verifyContexts();
                                          });
    }

    @Test
    public void testAttrsFromContextWithGeoDataVPN() {
        BindingContexts.runWithNewContext(() -> {
            final RequestContext ctxt = CurrentRequestContext.get();
            ctxt.setRequestId("Some request ID");
            ctxt.setDeviceType(new SimpleDeviceType(12345));
            ctxt.setDeviceId("Some device ID");
            ctxt.setCountry(NFCountry.ES);
            CurrentVisitor.set(new SimpleVisitor(5845828167678734L));
            ctxt.put(RequestAttributesManagerBaseServer.TIME_KEY, "1519708238000"); // 2018-02-27T05:10:38 GMT
            ctxt.setLocaleList("es,en");
            GeoRequestContextManager.instance().setIntoGeoRequestContext(ctxt, geoDataWithVpn());
            verifyContexts();
        });
    }

    private static GeoData geoDataWithVpn() {
        return new GeoDataImpl(Collections.singletonMap("blocked_proxy", "1"));
    }

    @Test
    public void testAttrsFromContextWithVMSVPN() {
        BindingContexts.runWithNewContext(() -> {
            final RequestContext ctxt = CurrentRequestContext.get();
            ctxt.setRequestId("Some request ID");
            ctxt.setDeviceType(new SimpleDeviceType(12345));
            ctxt.setDeviceId("Some device ID");
            ctxt.setCountry(NFCountry.ES);
            CurrentVisitor.set(new SimpleVisitor(5845828167678734L));
            ctxt.put(RequestAttributesManagerBaseServer.TIME_KEY, "1519708238000"); // 2018-02-27T05:10:38 GMT
            ctxt.setLocaleList("es,en");
            ctxt.addContext(RequestAttributesManagerBaseServer.IS_VPN_VMS_PROXY_KEY, "true");
            verifyContexts();
        });
    }

    @Test
    public void testContextFromAttrs() {
        final RequestAttributes attrs = manager.newRequestAttributesBuilder()
                                               .withNewRequestId()
                                               .withDeviceType(new SimpleDeviceType(43))
                                               .withDeviceId("I haven't seen you in a long long while")
                                               .withCountry(NFCountry.CH)
                                               .withVisitor(new SimpleVisitor(55893L))
                                               .withTime(66848713456L)
                                               .withGMTOffset((int) TimeUnit.HOURS.toMillis(2))
                                               .withTimeZone(TimeZone.getTimeZone("GMT+2"))
                                               .withLocales("it-CH", "fr-CH", "de-CH")
                                               .withExtraAttribute("zuul_tvui_uiVersion", "1.2.3.4.5")
                                               .withExtraAttribute("zuul_samurai_deviceFormFactor", "JustRight")
                                               .withExtraAttribute("zuul_ios_currentUIType", "someType")
                                               .build();
        final Runnable r = () -> {
            verifyContexts();
            Assert.assertEquals(attrs, manager.getCurrentRequestAttributes());
        };
        manager.runWithRequestAttributes(attrs, r);
    }

    @edu.umd.cs.findbugs.annotations.SuppressFBWarnings("UPM_UNCALLED_PRIVATE_METHOD")
    private void verifyContexts(final RequestAttributes attrs) {
        manager.runWithRequestAttributes(attrs, () -> verifyContexts());
    }

    /**
     * Verify that the current RequestContext matches the current RequestAttributes.
     */
    private void verifyContexts() {
        final RequestAttributes attrs = manager.getCurrentRequestAttributes();
        final RequestContext ctxt = CurrentRequestContext.get();
        System.out.println("Verifing " + attrs + " against " + ctxt);
        Assert.assertEquals(ctxt.getRequestId(), attrs.getRequestId());
        Assert.assertEquals(ctxt.getDeviceType(), attrs.getDeviceType());
        Assert.assertEquals(ctxt.getDeviceId(), attrs.getDeviceId());
        Assert.assertEquals(ctxt.getCountry(), attrs.getCountry());
        Assert.assertEquals(CurrentVisitor.get(), attrs.getVisitor());
        Assert.assertEquals(ctxt.get(RequestAttributesManagerBaseServer.TIME_KEY),
                            Optional.ofNullable(attrs.getTime()).map(t -> t.toString()).orElse(null));
        Assert.assertEquals(ctxt.getGMTOffset(), attrs.getGMTOffset());
        Assert.assertEquals(ctxt.getTimezone(),
                            Optional.ofNullable(attrs.getTimeZone()).map(tz -> tz.getID()).orElse(null));
        Assert.assertEquals(Optional.ofNullable(ctxt.getLocaleList()).orElse(""), String.join(",", attrs.getLocales()));
        Assert.assertEquals(ctxt.getLocale(), attrs.getLocale());
        Assert.assertEquals(Optional.ofNullable(attrs.getZoneOffset()).map(o -> o.getTotalSeconds() * 1000).orElse(null),
                            attrs.getGMTOffset());
        try {
            Assert.assertEquals(RequestAttributesManagerBaseServer.isVpn(ctxt), attrs.isVPNProxy());
        } catch (ContextSerializationException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testClosures() {
        final RequestAttributes a1 = manager.newRequestAttributesBuilder()
                                            .withNewRequestId()
                                            .withCountry(NFCountry.US)
                                            .withLocales("en-US")
                                            .build();
        final RequestAttributes a2 = manager.newRequestAttributesBuilder()
                                            .withNewRequestId()
                                            .withCountry(NFCountry.MX)
                                            .withLocales("ee-MX")
                                            .build();
        testRunnables(a1, a2);
        testCallables(a1, a2);
        testSuppliers(a1, a2);
    }

    private void testRunnables(final RequestAttributes a1, final RequestAttributes a2) {
        final Runnable r = () -> {
            Assert.assertEquals(a1, manager.getCurrentRequestAttributes());
            verifyContexts();
            manager.runWithRequestAttributes(a2, () -> {
                                                     Assert.assertEquals(a2, manager.getCurrentRequestAttributes());
                                                     verifyContexts();
                                                 });
            Assert.assertEquals(a1, manager.getCurrentRequestAttributes());
            verifyContexts();
        };
        manager.makeClosure(a1, r).run();
    }

    private void testCallables(final RequestAttributes a1, final RequestAttributes a2) {
        final Callable<Boolean> c = () -> {
            Assert.assertEquals(a1, manager.getCurrentRequestAttributes());
            verifyContexts();
            manager.callWithRequestAttributes(a2, () -> {
                                                      Assert.assertEquals(a2, manager.getCurrentRequestAttributes());
                                                      verifyContexts();
                                                      return Boolean.TRUE;
                                                  });
            Assert.assertEquals(a1, manager.getCurrentRequestAttributes());
            verifyContexts();
            return Boolean.TRUE;
        };
        try {
            manager.makeClosure(a1, c).call();
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void testSuppliers(final RequestAttributes a1, final RequestAttributes a2) {
        final Supplier<Boolean> s = () -> {
            Assert.assertEquals(a1, manager.getCurrentRequestAttributes());
            verifyContexts();
            manager.getWithRequestAttributes(a2, () -> {
                                                     Assert.assertEquals(a2, manager.getCurrentRequestAttributes());
                                                     verifyContexts();
                                                     return Boolean.TRUE;
                                                 });
            Assert.assertEquals(a1, manager.getCurrentRequestAttributes());
            verifyContexts();
            return Boolean.TRUE;
        };
        manager.makeClosure(a1, s).get();
    }

}
