dependencies {
    implementation project(':request-attrs-api')
    implementation project(':request-attrs-core')
    implementation 'netflix:basicTypes'
    implementation 'netflix:abzuulcontext:latest.release'
    implementation 'com.netflix.microcontext:microcontext-access:latest.release'

    implementation 'com.google.code.findbugs:annotations' // @Nonnull etc
    implementation 'javax.inject:javax.inject'
    implementation "netflix:geoip-common:latest.release"

    testImplementation 'junit:junit'
    testRuntimeOnly 'org.junit.vintage:junit-vintage-engine'
}
tasks.withType(Test).configureEach { 
    useJUnitPlatform()
}
