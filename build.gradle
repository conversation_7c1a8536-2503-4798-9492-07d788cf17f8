buildscript {
    final netflix_grpc_plugins_version = System.properties['netflix.grpc-plugins_netflix-grpc-plugins.version']
    dependencies {
        classpath "netflix.grpc-plugins:netflix-grpc-plugin-client:${netflix_grpc_plugins_version}"
        classpath "netflix.grpc-plugins:netflix-grpc-plugin-proto-definition:${netflix_grpc_plugins_version}"
        classpath "netflix.grpc-plugins:netflix-grpc-plugin-server-guice:${netflix_grpc_plugins_version}"
        classpath 'netflix:nebula-lumen-dashboard-plugin:latest.release'
        classpath 'com.netflix.spring.gradle:spring-boot-netflix-gradle-plugin:2.7.+'
    }
}

allprojects {
    apply plugin: 'netflix.nebula'
    apply plugin: 'nebula.release'
    apply plugin: 'netflix.spring-boot-netflix'
}

// We use the runtime dependency recommendation rules to resolve dependencies.
dependencyRecommendations {
  mavenBom module: "netflix.bom:runtime-platform-recommendations:latest.release"
}

subprojects {

    apply plugin: 'netflix.jvm-library'
    compileJava {
        options.compilerArgs << '-Xlint:all'
        options.compilerArgs << '-proc:none'
    }
    compileTestJava {
        options.compilerArgs << '-Xlint:all'
        options.compilerArgs << '-proc:none'
    }

    apply plugin: 'jacoco'

    apply plugin: 'netflix.spotbugs-ci'
    // spotbugs {
    //     toolVersion = '3.1.3'
    // }
    spotbugs {
        reportLevel = 'low'
        effort = 'max'
        ignoreFailures = false
        excludeFilter = new File(rootDir, '.config/spotbugs/spotbugs-exclude.xml')
    }
    spotbugsTest.enabled = false

    apply plugin: 'netflix.checkstyle'
    checkstyle {
        toolVersion = '9.3' //latest version to get InterfaceMemberImpliedModifier
        showViolations = true
        ignoreFailures = true   // would like this to be "false"
        configDirectory = new File(rootDir, '.config/checkstyle')
    }
    checkstyleTest.onlyIf { false } // Don't care about checking unit tests

    apply plugin: 'nebula.javadoc-jar'
    tasks.withType(Javadoc) {
        options.addStringOption('Xdoclint:html,reference,syntax', '-quiet')
        options.showFromProtected()
        options.noQualifiers = [ 'java.io', 'java.lang', 'java.net', 'java.text', 'java.time', \
                                'java.util', 'java.util.*', \
                                'javax.inject', 'javax.annotation' ]
        options.version = true
    }

    group = "com.netflix.request"

    configurations.all {
        exclude group: 'com.oracle'
        exclude group: 'org.apache.ant', module: 'ant'
        exclude group: 'org.apache.cxf', module: 'cxf'
    }

}
