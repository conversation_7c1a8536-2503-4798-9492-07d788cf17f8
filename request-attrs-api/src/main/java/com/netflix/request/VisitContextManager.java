package com.netflix.request;

import com.netflix.request.protogen.RequestAttributesProto;
import com.netflix.request.protogen.VisitContext;
import com.netflix.type.Visitor;

import java.util.concurrent.Callable;
import java.util.function.Supplier;
import javax.annotation.Nullable;

/**
 * Utility class for passing {@code RequestAttributes} data between clients and servers.
 * <p>
 * Applications often need to pass {@code RequestAttributes} along when they call some other service.
 * The Netflix base-server and gRPC extensions do that (for the most part) transparently,
 * automatically including the current {@code RequestContext} as out-of-band information along
 * with the request and installing it when the request is processed.
 * <p>
 * There are a few issues with this:
 * <ul>
 *   <li>The current user is not included in the {@code RequestConext}
 *   <li>Some developers dislike implicit parameters
 *   <li>Not all applications are built from the base-server
 *   <li>It isn't easy/possible to set the implicit data in smoke tests or integration tests
 *   <li>The Swagger UI doesn't make it easy/possible to construct the context
 * </ul>
 * <p>
 * The {@code VisitContext} protobuf type addresses these problems.
 * It can be passed as an explicit request parameter while still supporting
 * the default base-server handling of the {@code RequestContext}.
 * It does this by including a {@code RequestAttributes} object along with a flag indicating
 * how this should be combined with the implicit context.
 * <p>
 * The {@link VisitContextManager} makes it easy(?) for applications to use {@code VisitContext}.
 * On the caller side:
 * <ul>
 *   <li>The {@link VisitContextManager#makeImplicitVisitContext() makeImplicitVisitContext()}
 *       method creates a minimal {@code VisitContext} that uses the implicit {@code RequestContext}
 *   <li>The {@link VisitContextManager#makeExplicitVisitContext() makeExplicitVisitContext()}
 *       method creates a fully populated {@code VisitContext} built from the current {@code RequestAttributes}
 *   <li>The {@link VisitContextManager#makeExplicitVisitContext(RequestAttributes) makeExplicitVisitContext(RequestAttributes)}
 *       method creates a fully populated {@code VisitContext} built from the supplied argument
 * </ul>
 * <p>
 * On the server side, code that handles a request can install the supplied {@code VisitContext} a couple of ways:
 * <ul>
 *   <li>The {@link VisitContextManager#callWithVisitContext(VisitContext,Callable) callWithVisitContext(VisitContext,Callable)}
 *       executes the given {@code Callable} with the {@code VisitContext}
 *       installed as the current {@code RequestAttributes}
 *   <li>The {@link VisitContextManager#getWithVisitContext(VisitContext,Supplier) getWithVisitContext(VisitContext,Supplier)}
 *       executes the given {@code Supplier} with the {@code VisitContext}
 *       installed as the current {@code RequestAttributes}
 *   <li>The {@link VisitContextManager#runWithVisitContext(VisitContext,Runnable) runWithVisitContext(VisitContext,Runnable)}
 *       executes the given {@code Runnable} with the {@code VisitContext}
 *       installed as the current {@code RequestAttributes}
 * </ul>
 *
 * <h2>Examples</h2>
 * Calling a gRPC service with the current context:
 * <pre>
 * &#x40;Inject
 * VisitContextManager heler;
 *    ...
 *    FooReply reply =
 *        clientBlockingStub.getFoo(FooRequest.newBuilder()
 *                                            .setContext(manager.makeImplicitVisitContext())
 *                                            ... // set other fields in the request
 *                                            .build());
 * </pre>
 * <p>
 * Processing a request with a supplied {@code VisitContext}:
 * <pre>
 * public class FooServiceImpl extends FooServiceGrpc.FooServiceImplBase {
 *
 *     &#x40;Inject
 *     VisitContextManager heler;
 *     ...
 *     &#x40;Override
 *
 *     public void getFoo(FooRequest req, StreamObserver&lt;FooReply&gt; responseObserver) {
 *         manager.runWithVisitContext(req.getContext(), () -&gt; getFooInContext(req, responseObserver));
 *     }
 *
 *     private void getFooInContext(FooRequest req, StreamObserver&lt;FooReply&gt; responseObserver) {
 *         // When this is run the current {@code RequestAttributes} are set up,
 *         // independent of how the VisitContext was constructed
 *         ...
 *     }
 * }
 * </pre>
 */
public interface VisitContextManager {

    /*
     * Converting between Protobuf and "normal" representations of RequestAttributes
     */

    /**
     * Converts a {@code RequestAttributes} into an equivalent Protobuf {@code RequestAttributesProto}.
     */
    RequestAttributesProto toProtobuf(RequestAttributes attrs);

    /**
     * Converts a Protobuf {@code RequestAttributesproto} into an equivalent {@code RequestAttributes}.
     */
    RequestAttributes fromProtobuf(RequestAttributesProto proto);

    /**
     * Gets the {@code RequestAttributes} defined by a {@code VisitContext}.
     * This takes care of merging attributes into the current (implicit) attributes
     * if the VisitContext indicates that it should.
     */
    RequestAttributes fromProtobuf(VisitContext context);

    /**
     * Creates a new {@code VisitContext} that uses the implicit {@code RequestContext} for data.
     */
    VisitContext makeImplicitVisitContext();

    /**
     * Creates a new {@code VisitContext} that uses the implicit {@code RequestContext} for data.
     * @param visitor the {@code Visitor} to pass explicitly in the contructed {@code VisitContext},
     *    since the {@code RequestContest} doesn't hold the current user
     */
    VisitContext makeImplicitVisitContext(Visitor visitor);

    /**
     * Creates a new {@code VisitContext} protobuf object that passes all fields explicitly.
     * Data is taken from the current {@code RequestAttributes}.
     */
    VisitContext makeExplicitVisitContext();

    /**
     * Creates a new {@code VisitContext} protobuf object that passes all fields explicitly.
     */
    VisitContext makeExplicitVisitContext(RequestAttributes reqAttrs);

    /*
     * Methods for invoking code in the context of a VisitContext
     */

    /**
     * Calls a {@code Callable} in a context where the current {@code RequestAttributes} is defined
     * by the given {@code VisitContext}.
     * If the context is null, execution will be done with the current {@code RequestAttributes}.
     * If there is no current {@code RequestAttributes} a new empty one will be used.
     */
    <T> T callWithVisitContext(@Nullable VisitContext context, Callable<T> callable) throws Exception;

    /**
     * Creates a new {@code Callable} that, when called, will execute the given {@code Callable}
     * in a context where the current {@link RequestAttributes} is defined by the given {@code VisitContext}.
     * If the context is null, execution will be done with the current {@code RequestAttributes}.
     * If there is no current {@code RequestAttributes} a new empty one will be used.
     */
    default <T> Callable<T> makeClosure(final VisitContext context, final Callable<T> callable) {
        return () -> callWithVisitContext(context, callable);
    }

    /**
     * Runs a {@code Runnable} in a context where the current {@code RequestAttributes} is defined
     * by the given {@code VisitContext}.
     * If the context is null, execution will be done with the current {@code RequestAttributes}.
     * If there is no current {@code RequestAttributes} a new empty one will be used.
     */
    void runWithVisitContext(VisitContext context, Runnable runnable);

    /**
     * Creates a new {@code Runnable} that, when called, will execute the given {@code Callable}
     * in a context where the current {@link RequestAttributes} is defined by the given {@code VisitContext}.
     * If the context is null, execution will be done with the current {@code RequestAttributes}.
     * If there is no current {@code RequestAttributes} a new empty one will be used.
     */
    default Runnable makeClosure(final VisitContext context, final Runnable runnable) {
        return () -> runWithVisitContext(context, runnable);
    }

    /**
     * Executes a {@code Supplier} in a context where the current {@code RequestAttributes} is defined
     * by the given {@code VisitContext}.
     * If the context is null, execution will be done with the current {@code RequestAttributes}.
     * If there is no current {@code RequestAttributes} a new empty one will be used.
     */
    <T> T getWithVisitContext(VisitContext context, Supplier<T> supplier);

    /**
     * Creates a new {@code Supplier} that, when called, will execute the given {@code Callable}
     * in a context where the current {@link RequestAttributes} is defined by the given {@code VisitContext}.
     * If the context is null, execution will be done with the current {@code RequestAttributes}.
     * If there is no current {@code RequestAttributes} a new empty one will be used.
     */
    default <T> Supplier<T> makeClosure(final VisitContext context, final Supplier<T> supplier) {
        return () -> getWithVisitContext(context, supplier);
    }

 } // VisitContextManager
