package com.netflix.request;

import com.netflix.request.protogen.UiFlavor;
import com.netflix.type.DeviceType;
import com.netflix.type.ISOCountry;
import com.netflix.type.Visitor;

import java.io.Serializable;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import javax.annotation.Nullable;

/**
 * Holder of attributes for a request to a service.
 * Implementations must support Java Serialization.
 */
@SuppressWarnings("serial")
public interface RequestAttributes extends Serializable {

    /**
     * A builder for {@link RequestAttributes} objects.
     * @see RequestAttributesManager#newRequestAttributesBuilder()
     */
    public static interface Builder {

        /**
         * Copies values from the given {@code RequestAttributes}.
         * Any previous values set in the builder are forgotten.
         * <p>
         * <b>Note:</b>
         * The copy may not have the same "effective time" as the source.
         * @return this builder
         */
        Builder copying(RequestAttributes reqAttrs);

        /**
         * Sets the ID of the request.
         * @return this builder
         */
        Builder withRequestId(@Nullable String id);

        /**
         * Sets the ID of the request to a new unique value.
         */
        Builder withNewRequestId();

        /**
         * Sets the track-ID of the request.
         * @return this builder
         */
        Builder withTrackId(@Nullable String id);

        /**
         * Sets the DeviceType for the request.
         * @return this builder
         */
        Builder withDeviceType(@Nullable DeviceType deviceType);

        /**
         * Sets the ID of the device as specified for the request.
         * @return this builder
         */
        Builder withDeviceId(@Nullable String id);

        /**
         * Sets the "UIFlavor" property of the request.
         * @return this builder
         */
        default Builder withUIFlavor(final UiFlavor flavor) {
            return this;
        }

        /**
         * Sets the country specified for the request.
         * @return this builder
         */
        Builder withCountry(@Nullable ISOCountry country);

        /**
         * Sets the visitor specified for the request.
         * @return this builder
         */
        Builder withVisitor(@Nullable Visitor visitor);

        /**
         * Sets the time specified for the request.
         * @return this builder
         * @see #withInstant(Instant)
         */
        Builder withTime(@Nullable Long millis);

        /**
         * Sets the time specified for the request.
         * @return this builder
         * @see #withTime(Long)
         */
        Builder withInstant(@Nullable Instant instant);

        /**
         * Sets the {@code ZoneOffset} for the request.
         * This also sets the GMT offset (see {@link #withGMTOffset(Integer)}).
         * @return this builder
         */
        Builder withZoneOffset(@Nullable ZoneOffset offset);

        /**
         * Sets the timezone offset from GMT (in milliseconds) for the request.
         * This also sets the ZoneOffset (see {@link #withZoneOffset(ZoneOffset)}).
         * @return this builder
         */
        Builder withGMTOffset(@Nullable Integer offset);

        /**
         * Sets the timezone specified for the request.
         * This does <em>not</em> set the GMT offset.
         * @return this builder
         */
        Builder withTimeZone(@Nullable TimeZone tz);

        /**
         * Sets the list of preferred locales specified for the request.
         * @return this builder
         */
        Builder withLocales(List<String> locales);

        /**
         * Sets the list of preferred locales specified for the request.
         * @return this builder
         */
        Builder withLocales(String... locales);

        /**
         * Adds the given locales specified for the request.
         * @return this builder
         */
        Builder addingLocales(String... locales);

        /**
         * Sets the content-preview-ID of the request.
         * @return this builder
         */
        Builder withContentPreviewId(@Nullable String id);

        /**
         * Sets an extra attribute of the request.
         * @return this builder
         */
        Builder withExtraAttribute(String name, @Nullable String value);

        /**
         * Sets the collection of extra attributes for the request.
         * @return this builder
         */
        Builder withExtraAttributes(Map<String, String> attributes);

        /**
         * Sets the "isArtificial" property of the request.
         * @return this builder
         */
        Builder withIsArtificial(boolean flag);

        /**
         * Sets the "isVPNProxy" property of the request.
         * @return this builder
         */
        Builder withIsVPNProxy(boolean flag);

        /**
         * Sets the "isNonMember" property of the request.
         * @return this builder
         */
        Builder withIsNonMember(boolean flag);

        /**
         * Gets the constructed {@code RequestAttributes} from the builder.
         */
        RequestAttributes build();

    } // Builder

    /*
     * IMPORTANT!
     *
     * If you add fields to this object, you must update the AbstractRequestAttributes class
     * to maintain the proper equality constraints.
     * You also need to update the AbstractBuilder::copying method
     */

    /**
     * Gets the ID of the current request.
     * @return null if the value is not specified in the request
     */
    @Nullable
    String getRequestId();

    /**
     * Gets the track-id of the request.
     * @return null if the value is not specified in the request
     */
    @Nullable
    String getTrackId();

    /**
     * Gets the DeviceType for the request.
     * @return null if the value is not specified in the request
     */
    @Nullable
    DeviceType getDeviceType();

    /**
     * Gets the ID of the device as specified for the request.
     * @return null if the value is not specified in the request
     */
    @Nullable
    String getDeviceId();

    /**
     * Gets the device "UI Flavor" of the current request.
     * See <a href='http://go.netflix.com/clientlist'>Client Applications spreadsheet</a>.
     * @return null if the flavor cannot be determined or is not specified
     */
    default UiFlavor getUIFlavor() {
        return null;
    }

    /**
     * Gets the country specified for the request.
     * @return null if the value is not specified in the request
     */
    @Nullable
    ISOCountry getCountry();

    /**
     * Gets the visitor specified for the request.
     * @return null if the value is not specified in the request
     */
    @Nullable
    Visitor getVisitor();

    /**
     * Gets the time specified for the request.
     * @return null if the value is not specified in the request
     * @see #getInstant()
     */
    @Nullable
    Long getTime();

    /**
     * Gets the time specified for the request.
     * @return null if the value is not specified in the request
     * @see #getTime()
     */
    @Nullable
    Instant getInstant();

    /**
     * Gets the "effective" time of the request.
     * @return the time specified in the request, if it is present;
     *    otherwise the create time of the object
     * @see #getEffectiveInstant()
     */
    long getEffectiveTime();

    /**
     * Gets the "effective" time of the request.
     * @return the time specified in the request, if it is present;
     *    otherwise the create time of the object
     * @see #getEffectiveTime()
     */
    Instant getEffectiveInstant();

    /**
     * Gets the {@code ZoneOffset} specified for the request.
     * @return null if no time-zone info is specified in the request
     */
    @Nullable
    ZoneOffset getZoneOffset();

    /**
     * Gets the timezone offset from GMT (in milliseconds) of the requesting device.
     * It is probably better to use {@link #getZoneOffset()}.
     * @return null if no GMT offset is specified in the request
     */
    @Nullable
    Integer getGMTOffset();

    /**
     * Gets the timezone specified for the request.
     * It is probably better to use {@link #getZoneOffset()}.
     * @return null if the value is not specified in the request
     */
    @Nullable
    TimeZone getTimeZone();

    /**
     * Gets the list of preferred locales specified for the request.
     * @return an empty list if no locales are specified in the request
     */
    List<String> getLocales();

    /**
     * Gets the primary locale specified for the request.
     * @return null if no locales are specified in the request
     */
    @Nullable
    String getLocale();

    /**
     * Gets the Content Preview ID, if any, for the request.
     * @return null if no content preview is specified in the request
     */
    @Nullable
    String getContentPreviewId();

    /**
     * Gets all the extra attribute from the request.
     * @return an empty map if there are no extra attributes
     */
    Map<String, String> getExtraAttributes();

    /**
     * Gets an extra attribute from the request.
     * @return an empty Optional if there is no extra attribute with the given name
     */
    Optional<String> getExtraAttribute(String name);

    /**
     * Is this an artificial request, created for testing purposes?
     * If it is, applications should avoid modifying production databases.
     * @return false if this request is the result of a visit by a customer, true otherwise
     */
    boolean isArtificial();

    /**
     * Is the current request coming through a (VPN) proxy?
     */
    boolean isVPNProxy();

    /**
     * Is the current request originating for a non member.
     */
    boolean isNonMember();
}
