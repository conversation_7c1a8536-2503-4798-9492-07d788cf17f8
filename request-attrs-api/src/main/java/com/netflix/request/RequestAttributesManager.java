package com.netflix.request;

import com.netflix.type.Visitor;

import java.util.concurrent.Callable;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.function.Supplier;

/**
 * Manager for accessing the {@link RequestAttributes} for the current request.
 * <p>
 * {@code RequestAttributes} objects are installed as "current" indirectly,
 * by invoking a closure created by one of the {@code makeClosure(RequestAttributes,...)}
 * methods defined in this class.
 */
public interface RequestAttributesManager {

    /**
     * Gets a new builder of a {@link RequestAttributes}.
     */
    RequestAttributes.Builder newRequestAttributesBuilder();

    /**
     * Gets the {@code RequestAttributes} for the current request.
     * @throws IllegalStateException if there is no current {@code RequestAttributes}.
     * @see #hasCurrentRequestAttributes()
     */
    RequestAttributes getCurrentRequestAttributes();

    /**
     * Is there a current {@code RequestAttributes} defined?
     */
    default boolean hasCurrentRequestAttributes() {
        try {
            getCurrentRequestAttributes();
            return true;
        } catch (IllegalStateException e) {
            return false;
        }
    }

    /*
     * Methods for invoking code in the context of a RequestAttributes.
     */

    /**
     * Executes the given {@code Callable} in a context where the given {@link RequestAttributes} is "current".
     */
    <T> T callWithRequestAttributes(RequestAttributes attrs, Callable<T> callable) throws Exception;

    /**
     * Creates a new {@code Callable} that, when called, will execute the given {@code Callable}
     * in a context where the given {@link RequestAttributes} is "current".
     */
    default <T> Callable<T> makeClosure(final RequestAttributes attrs, final Callable<T> callable) {
        return () -> callWithRequestAttributes(attrs, callable);
    }

    /**
     * Creates a new {@code Callable} that, when called, will execute the given {@code Callable}
     * in a context where the current {@link RequestAttributes} is "current".
     * This is useful when constructing code to run in some other thread.
     */
    default <T> Callable<T> makeClosure(final Callable<T> callable) {
        return makeClosure(getCurrentRequestAttributes(), callable);
    }

    /**
     * Executes the given {@code Runnable} in a context where the given {@link RequestAttributes} is "current".
     */
    void runWithRequestAttributes(RequestAttributes attrs, Runnable runnable);

    /**
     * Creates a new {@code Runnable} that, when run, will execute the given {@code Runnable}
     * in a context where the given {@link RequestAttributes} is "current".
     */
    default Runnable makeClosure(final RequestAttributes attrs, final Runnable runnable) {
        return () -> runWithRequestAttributes(attrs, runnable);
    }

    /**
     * Creates a new {@code Runnable} that, when run, will execute the given {@code Runnable}
     * in a context where the current {@link RequestAttributes} is "current".
     * This is useful when constructing code to run in some other thread.
     */
    default Runnable makeClosure(final Runnable runnable) {
        return makeClosure(getCurrentRequestAttributes(), runnable);
    }

    /**
     * Invokes the given {@code Supplier} in a context where the given {@link RequestAttributes} is "current".
     */
    <T> T getWithRequestAttributes(RequestAttributes attrs, Supplier<T> supplier);

    /**
     * Creates a new {@code Supplier} that, when invoked, will invoke the given {@code Supplier}
     * in a context where the given {@link RequestAttributes} is "current".
     */
    default <T> Supplier<T> makeClosure(final RequestAttributes attrs, final Supplier<T> supplier) {
        return () -> getWithRequestAttributes(attrs, supplier);
    }

    /**
     * Creates a new {@code Supplier} that, when invoked, will invoke the given {@code Supplier}
     * in a context where the current {@link RequestAttributes} is "current".
     * This is useful when constructing code to run in some other thread.
     */
    default <T> Supplier<T> makeClosure(final Supplier<T> supplier) {
        return makeClosure(getCurrentRequestAttributes(), supplier);
    }

    /*
     * Ways to run code, preserving the most of the context, but changing the visitor
     */

    /**
     * Executes the {@code Callable} in a context where the {@code RequestAttributes}
     * is a copy of the current context, but with the visitor set to the given {@code Visitor}.
     * @throws IllegalStateException if there is no current {@code RequestAttributes}.
     */
    <T> T callWithVisitor(final Visitor visitor, Callable<T> callable) throws Exception;

    /**
     * Executes the {@code Runnable} in a context where the {@code RequestAttributes}
     * is a copy of the current context, but with the visitor set to the given {@code Visitor}.
     * @throws IllegalStateException if there is no current {@code RequestAttributes}.
     */
    void runWithVisitor(final Visitor visitor, Runnable runnable);

    /**
     * Executes the {@code Supplier} in a context where the {@code RequestAttributes}
     * is a copy of the current context, but with the visitor set to the given {@code Visitor}.
     * @throws IllegalStateException if there is no current {@code RequestAttributes}.
     */
    <T> T getWithVisitor(final Visitor visitor, Supplier<T> supplier);

    /*
     * Executors that automatically capture the current RequestAttributes when code is submitted
     */

    /**
     * Wraps an {@code Executor}, producing one that ensures that any code submitted for
     * execution is run with the current {@code RequestAttributes} set as current,
     * independent of the thread the code is actually run on.
     *
     * @param delegate The real executor
     */
    Executor wrap(final Executor delegate);

    /**
     * Creates a new {@code ExecutorService} that delegates behavior to a given {@code ExecutorService},
     * ensuring that all tasks handed to the service are "wrapped" in the binding context that was
     * active at the time the task was submitted.
     * @param delegate The real executor-service
     * @return ExecutorService that propagate the current context for anything executed on the delegates executor
     */
    ExecutorService wrap(final ExecutorService delegate);

} // RequestAttributesManager
