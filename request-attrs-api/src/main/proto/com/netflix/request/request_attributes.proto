syntax = "proto3";

package com.netflix.request;

import "com/netflix/request/ui_flavor.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "netflix/basicTypes/basic_types.proto";

import "com/netflix/proto/options/docs/api-docs.proto";

option java_package = "com.netflix.request.protogen";
option java_multiple_files = true;
option java_generate_equals_and_hash = true;

message RequestAttributesProto {
  option (com.netflix.proto.options.docs.model) = {
    description: "A Protobuf version of the RequestAttributes object."
  };

  reserved 5;

  // Wrap these values so we can tell if they are set or not
  message LocaleList {
    repeated string locales                    = 1;
  }
  message ExtraAttributes {
    map<string, string> extraAttributes        = 1;
  }

  // Fields corresponding to values in the RequestAttributes object

  google.protobuf.StringValue requestId        = 1;
  google.protobuf.StringValue trackId          = 2;
  .netflix.basicTypes.DeviceType deviceType    = 3;
  google.protobuf.StringValue deviceId         = 4;
  .netflix.basicTypes.ISOCountry country       = 6;
  .netflix.basicTypes.Visitor visitor          = 7;
  google.protobuf.Timestamp time               = 8;
  google.protobuf.Duration zoneOffset          = 9;
  google.protobuf.StringValue timeZone         = 10;
  LocaleList locales                           = 11;
  google.protobuf.StringValue contentPreviewId = 12;
  ExtraAttributes extraAttributes              = 13;
  google.protobuf.BoolValue isArtificial       = 14;
  google.protobuf.BoolValue isVPNProxy         = 15;
  UiFlavor uiFlavorEnum                        = 16;
  google.protobuf.BoolValue isNonMember        = 17;
}


message VisitContext {
  option (com.netflix.proto.options.docs.model) = {
    description: "RequestAttributes that can be used as a gRPC parameter.\n"
    "This includes information specifying how the supplied data should be combined with implicit context data."
  };

  enum Usage {
    USE_IMPLICIT = 0;           // Must be first, to capture default case
    IGNORE_IMPLICIT = 1;
    EXTEND_IMPLICIT = 2;
  }

  Usage usage = 1 [ (com.netflix.proto.options.docs.field).description =
                    "How should the supplied RequestAttributes be used?" ];

  RequestAttributesProto request_attributes = 2 [ (com.netflix.proto.options.docs.field).description =
                                                  "The RequestAttributes to use in this visit context." ];
}
