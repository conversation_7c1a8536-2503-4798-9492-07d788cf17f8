syntax = "proto3";

package com.netflix.request;

option java_package = "com.netflix.request.protogen";
option java_multiple_files = true;
option java_generate_equals_and_hash = true;

/**
 * An enumeration of all the identifiers that various client applications use.
 * These are derived from http://go.netflix.com/clientlist
 */
enum UiFlavor {
    UNKNOWN_UI_FLAVOR       = 0;
    AKIRA                   = 1;    // Owners: Member Web UI, CBP | Devices: Website + HTML5 player & Silverlight
    ANDROID                 = 2;    // Owners: Android Team | Devices: Android phone & tablet
    ARGO                    = 3;    // Owners: iOS UI | Devices: iOS Mobile Native & iOS Tablet Native
    ATV_FUJI                = 4;    // Owners: SCT | Devices: Apple TV 2 & 3
    ATV_HOPPER              = 5;    // Owners: iOS | Devices: Apple TV 2015
    DARWIN                  = 6;    // Owners: TVUI | Devices: Android TV, Nintendo Wii U (Gibbon), NRDP 4.0-5.0 CE, Roku NRDP, Sony PlayStation 3 (Gibbon), Sony PlayStation 4, Xbox 360 (Gibbon), & Xbox One (<PERSON>ibbon)
    IOS_LEGACY              = 7;    // Owners: Phone & Tablet UI | Devices: iOS Mobile Web, iOS Tablet Web (iPad 1) & iOS Tablet Web (iPad 2+)
    TV_OTHER                = 8;    // Owners: Cast team, CBP, Partners, NCCP, Roku, Sony, Edge & TVUI | Devices: Cast (Cadmium), Mediaroom, NRDP 1.x CE, Roku Brightscript, Sony BIVL, Google TV 4.0, NRDP 2.0-3.0 CE, NRDP 3.1 CE (Flash), NRDP 3.1 CE (WebKit GTV Marvell), NRDP 3.1 CE (WebKit), NRDP 3.2 CE, NRDP 3.2 CE (Samsung), NRDP 3.x CE (LG Pointer) & Sony PlayStation Vita
    WINDOWS_COUGAR          = 9;    // Owners: CBP | Devices: Windows Phone 7/8
    WINDOWS_GOTHAM          = 10;   // Owners: CBP | Devices: Windows 8
    WINDOWS_PX              = 11;   // Owners: CBP | Devices: Windows 10
    WINDOWS_WILDCAT         = 12;   // Owners: CBP | Devices: Windows Phone 8.1
    ECLIPSE                 = 13;   // Owners: TVUI and Discovery Systems
    ATV_ECLIPSE             = 14;   // Owners: iOS | Devices: Apple TV devices supporting Eclipse
}