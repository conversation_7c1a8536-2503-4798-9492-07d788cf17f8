### Making Changes to the proto files or adding new ones

After making changes make sure to execute following commands. Note that running them on the top level folder also works, you don't need to run it in this directory.

1. Make sure that there are no errors in the protos

```shell
./gradlew checkProto
```

2. Then once previous step succeeds run

```shell
./gradlew generateProto generateProtoLock
```

3 (WARNING). In the rare cases when you made a backwards incompatible change and have taken all the precautions to make
sure none of the users of these will be broken (i.e. you are removing an unused field) you can also
execute `deleteProtoLock` command. This is not recommended and can lead to issues, but listing here for completeness.

```shell
./gradlew deleteProtoLock generateProto generateProtoLock
```

### Protocol Buffers Gradle Commands

<a href="http://manuals.test.netflix.net/view/grpc/mkdocs/master/reference/GRADLE_SUPPORT/#generateproto">generate
proto</a><br/>
<a href="http://manuals.test.netflix.net/view/grpc/mkdocs/master/reference/GRADLE_SUPPORT/#generateprotolock">generate
protolock</a>