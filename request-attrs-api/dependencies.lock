{"compileClasspath": {"com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1"}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx"]}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.8", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.8", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.32"}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix:basicTypes": {"locked": "1.87.0", "transitive": ["netflix:basicTypes-proto-bridge"]}, "netflix:basicTypes-proto": {"locked": "1.87.0", "transitive": ["netflix:basicTypes-proto-bridge"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.87.0"}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}}, "compileProtoPath": {"com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1", "transitive": ["netflix:basicTypes", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.gson:gson": {"locked": "2.8.9", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.8", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:basicTypes-proto"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.8", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.32", "transitive": ["netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto"]}, "netflix:basicTypes": {"locked": "1.87.0", "transitive": ["netflix:basicTypes-proto-bridge"]}, "netflix:basicTypes-proto": {"locked": "1.87.0", "transitive": ["netflix:basicTypes-proto-bridge"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.87.0"}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}}, "protobufToolsLocator_grpc_java": {"io.grpc:protoc-gen-grpc-java": {"locked": "1.39.0"}}, "protobufToolsLocator_netflix_grpc_wrapper_type": {"netflix.grpc-plugins:netflix-grpc-plugin-protoc-wrapper-type": {"locked": "7.5.11"}}, "protobufToolsLocator_protoc": {"com.google.protobuf:protoc": {"locked": "3.22.2"}}, "protolock": {"nilslice:protolock": {"locked": "20210218T172155Z"}}, "resolutionRules": {"com.netflix.nebula:gradle-resolution-rules": {"locked": "0.82.0", "transitive": ["netflix:request-attrs"]}, "netflix.nebula.resolutionrules:resolution-rules": {"locked": "1.360.0", "transitive": ["netflix:request-attrs"]}, "netflix:request-attrs": {"project": true}}, "runtimeClasspath": {"com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1", "transitive": ["netflix:basicTypes", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.gson:gson": {"locked": "2.8.9", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.8", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:basicTypes-proto"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.8", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.32", "transitive": ["netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto"]}, "netflix:basicTypes": {"locked": "1.87.0", "transitive": ["netflix:basicTypes-proto-bridge"]}, "netflix:basicTypes-proto": {"locked": "1.87.0", "transitive": ["netflix:basicTypes-proto-bridge"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.87.0"}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}}, "testCompileClasspath": {"com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1"}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx"]}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.8", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.8", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.32"}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix:basicTypes": {"locked": "1.87.0", "transitive": ["netflix:basicTypes-proto-bridge"]}, "netflix:basicTypes-proto": {"locked": "1.87.0", "transitive": ["netflix:basicTypes-proto-bridge"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.87.0"}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}}, "testCompileProtoPath": {"com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1", "transitive": ["netflix:basicTypes", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.gson:gson": {"locked": "2.8.9", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.8", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:basicTypes-proto"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.8", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.32", "transitive": ["netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto"]}, "netflix:basicTypes": {"locked": "1.87.0", "transitive": ["netflix:basicTypes-proto-bridge"]}, "netflix:basicTypes-proto": {"locked": "1.87.0", "transitive": ["netflix:basicTypes-proto-bridge"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.87.0"}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}}, "testRuntimeClasspath": {"com.atomicjar:testcontainers-cloud-provider-strategy": {"locked": "0.0.43", "transitive": ["com.netflix.nebula.testcontainers:testcontainers-cloud-configurer"]}, "com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1", "transitive": ["netflix:basicTypes", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.gson:gson": {"locked": "2.8.9", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}, "com.netflix.nebula.testcontainers:testcontainers-cloud-configurer": {"locked": "1.18.0"}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.8", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:basicTypes-proto"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.8", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.32", "transitive": ["netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto"]}, "netflix:basicTypes": {"locked": "1.87.0", "transitive": ["netflix:basicTypes-proto-bridge"]}, "netflix:basicTypes-proto": {"locked": "1.87.0", "transitive": ["netflix:basicTypes-proto-bridge"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.87.0"}, "org.apiguardian:apiguardian-api": {"locked": "1.1.2", "transitive": ["org.junit.platform:junit-platform-commons", "org.junit.platform:junit-platform-engine", "org.junit.platform:junit-platform-launcher"]}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}, "org.junit.platform:junit-platform-commons": {"locked": "1.13.3", "transitive": ["org.junit.platform:junit-platform-engine"]}, "org.junit.platform:junit-platform-engine": {"locked": "1.13.3", "transitive": ["org.junit.platform:junit-platform-launcher"]}, "org.junit.platform:junit-platform-launcher": {"locked": "1.13.3"}, "org.opentest4j:opentest4j": {"locked": "1.3.0", "transitive": ["org.junit.platform:junit-platform-engine"]}}}