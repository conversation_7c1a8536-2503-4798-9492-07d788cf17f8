{"definitions": [{"protopath": "src:/:main:/:proto:/:com:/:netflix:/:request:/:request_attributes.proto", "def": {"enums": [{"name": "VisitContext.Usage", "enum_fields": [{"name": "USE_IMPLICIT"}, {"name": "IGNORE_IMPLICIT", "integer": 1}, {"name": "EXTEND_IMPLICIT", "integer": 2}]}], "messages": [{"name": "RequestAttributesProto", "fields": [{"id": 1, "name": "requestId", "type": "google.protobuf.StringValue"}, {"id": 2, "name": "trackId", "type": "google.protobuf.StringValue"}, {"id": 3, "name": "deviceType", "type": ".netflix.basicTypes.DeviceType"}, {"id": 4, "name": "deviceId", "type": "google.protobuf.StringValue"}, {"id": 6, "name": "country", "type": ".netflix.basicTypes.ISOCountry"}, {"id": 7, "name": "visitor", "type": ".netflix.basicTypes.Visitor"}, {"id": 8, "name": "time", "type": "google.protobuf.Timestamp"}, {"id": 9, "name": "zoneOffset", "type": "google.protobuf.Duration"}, {"id": 10, "name": "timeZone", "type": "google.protobuf.StringValue"}, {"id": 11, "name": "locales", "type": "LocaleList"}, {"id": 12, "name": "contentPreviewId", "type": "google.protobuf.StringValue"}, {"id": 13, "name": "extraAttributes", "type": "ExtraAttributes"}, {"id": 14, "name": "isArtificial", "type": "google.protobuf.BoolValue"}, {"id": 15, "name": "isVPNProxy", "type": "google.protobuf.BoolValue"}, {"id": 16, "name": "uiFlavorEnum", "type": "UiFlavor"}, {"id": 17, "name": "isNonMember", "type": "google.protobuf.BoolValue"}], "reserved_ids": [5], "messages": [{"name": "LocaleList", "fields": [{"id": 1, "name": "locales", "type": "string", "is_repeated": true}]}, {"name": "ExtraAttributes", "maps": [{"key_type": "string", "field": {"id": 1, "name": "extraAttributes", "type": "string"}}]}], "options": [{"name": "(com.netflix.proto.options.docs.model)", "aggregated": [{"name": "description", "value": "A Protobuf version of the RequestAttributes object."}]}]}, {"name": "VisitContext", "fields": [{"id": 1, "name": "usage", "type": "Usage", "options": [{"name": "(com.netflix.proto.options.docs.field).description", "value": "How should the supplied RequestAttributes be used?"}]}, {"id": 2, "name": "request_attributes", "type": "RequestAttributesProto", "options": [{"name": "(com.netflix.proto.options.docs.field).description", "value": "The RequestAttributes to use in this visit context."}]}], "options": [{"name": "(com.netflix.proto.options.docs.model)", "aggregated": [{"name": "description", "value": "RequestAttributes that can be used as a gRPC parameter.\\nThis includes information specifying how the supplied data should be combined with implicit context data."}]}]}], "imports": [{"path": "com/netflix/request/ui_flavor.proto"}, {"path": "google/protobuf/duration.proto"}, {"path": "google/protobuf/timestamp.proto"}, {"path": "google/protobuf/wrappers.proto"}, {"path": "netflix/basicTypes/basic_types.proto"}, {"path": "com/netflix/proto/options/docs/api-docs.proto"}], "package": {"name": "com.netflix.request"}, "options": [{"name": "java_package", "value": "com.netflix.request.protogen"}, {"name": "java_multiple_files", "value": "true"}, {"name": "java_generate_equals_and_hash", "value": "true"}]}}, {"protopath": "src:/:main:/:proto:/:com:/:netflix:/:request:/:ui_flavor.proto", "def": {"enums": [{"name": "UiFlavor", "enum_fields": [{"name": "UNKNOWN_UI_FLAVOR"}, {"name": "AKIRA", "integer": 1}, {"name": "ANDROID", "integer": 2}, {"name": "ARGO", "integer": 3}, {"name": "ATV_FUJI", "integer": 4}, {"name": "ATV_HOPPER", "integer": 5}, {"name": "DARWIN", "integer": 6}, {"name": "IOS_LEGACY", "integer": 7}, {"name": "TV_OTHER", "integer": 8}, {"name": "WINDOWS_COUGAR", "integer": 9}, {"name": "WINDOWS_GOTHAM", "integer": 10}, {"name": "WINDOWS_PX", "integer": 11}, {"name": "WINDOWS_WILDCAT", "integer": 12}, {"name": "ECLIPSE", "integer": 13}, {"name": "ATV_ECLIPSE", "integer": 14}]}], "package": {"name": "com.netflix.request"}, "options": [{"name": "java_package", "value": "com.netflix.request.protogen"}, {"name": "java_multiple_files", "value": "true"}, {"name": "java_generate_equals_and_hash", "value": "true"}]}}]}