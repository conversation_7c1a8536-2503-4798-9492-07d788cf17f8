package com.netflix.request.core;

import com.netflix.request.RequestAttributes;
import com.netflix.request.RequestAttributesManager;
import com.netflix.request.VisitContextManager;
import com.netflix.request.protogen.RequestAttributesProto;
import com.netflix.request.protogen.UiFlavor;
import com.netflix.request.protogen.VisitContext;
import com.netflix.type.NFCountry;
import com.netflix.type.SimpleDeviceType;
import com.netflix.type.SimpleVisitor;
import com.netflix.type.proto.ISOCountries;
import com.netflix.type.proto.Visitors;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import org.junit.Assert;
import org.junit.Test;

public class VisitContextTests {

    private final RequestAttributesManager manager = new RequestAttributesManagerThreadLocal();
    private final VisitContextManager helper = new StandardVisitContextManager(manager);

    private final RequestAttributes defaultAttrs = manager.newRequestAttributesBuilder()
                                                   .withNewRequestId()
                                                   .withDeviceType(new SimpleDeviceType(12345))
                                                   .withDeviceId("Some device ID")
                                                   .withUIFlavor(UiFlavor.AKIRA)
                                                   .withCountry(NFCountry.ES)
                                                   .withVisitor(new SimpleVisitor(5845828167678734L))
                                                   .withTime(1519708238000L) // 2018-02-27T05:10:38 GMT
                                                   .withGMTOffset((int) TimeUnit.HOURS.toMillis(2))
                                                   .withTimeZone(TimeZone.getTimeZone("GMT+2"))
                                                   .withLocales("es", "en")
                                                   .withExtraAttribute("tartan", "speckled")
                                                   .withIsArtificial(true)
                                                   .withIsVPNProxy(true)
                                                   .build();

    private final RequestAttributesProto contextAttrsProto = RequestAttributesProto.newBuilder()
                                                                                   .setRequestId("something")
                                                                                   .setVisitor(Visitors.toProtobuf(22143789L))
                                                                                   .setCountry(ISOCountries.toProtobuf("MX"))
                                                                                   .build();

    private final RequestAttributes contextAttrs = helper.fromProtobuf(contextAttrsProto);

    @Test
    public void testConversion() {
        Assert.assertEquals(contextAttrsProto.getRequestId().getValue(), contextAttrs.getRequestId());
        Assert.assertEquals(contextAttrsProto.getVisitor().getId(), contextAttrs.getVisitor().getId().longValue());
        Assert.assertEquals(contextAttrsProto.getCountry().getId(), contextAttrs.getCountry().getId());

        // Test round trip from POJO to Proto to POJO
        Assert.assertEquals(defaultAttrs, helper.fromProtobuf(helper.toProtobuf(defaultAttrs)));
    }

    @Test
    public void testVisitContextExplicit() {
        final VisitContext vc = VisitContext.newBuilder()
                                            .setUsage(VisitContext.Usage.IGNORE_IMPLICIT)
                                            .setRequestAttributes(contextAttrsProto)
                                            .build();

        checkExpected(vc, (computedAttrs) -> {

                              System.out.println("Checking " + vc + " -> " + computedAttrs);

                              // Check fields that should be copied from the contextAttrs
                              Assert.assertEquals(contextAttrs.getRequestId(), computedAttrs.getRequestId());
                              Assert.assertEquals(contextAttrs.getCountry(), computedAttrs.getCountry());
                              Assert.assertEquals(contextAttrs.getVisitor(), computedAttrs.getVisitor());

                              // Check that other fields are not copied from the implicit attrs
                              Assert.assertNull(computedAttrs.getDeviceId());
                              Assert.assertEquals(contextAttrs.getLocales().size(), computedAttrs.getLocales().size());
                              Assert.assertEquals(contextAttrs, computedAttrs);
                          });

    }

    @Test
    public void testVisitContextImplicit() {
        final VisitContext vc = VisitContext.newBuilder()
                                            .setUsage(VisitContext.Usage.EXTEND_IMPLICIT)
                                            .setRequestAttributes(contextAttrsProto)
                                            .build();

        checkExpected(vc, (computedAttrs) -> {

                              System.out.println("Checking " + vc + " -> " + computedAttrs);

                              // Check fields that should be copied from the contextAttrs
                              Assert.assertEquals(contextAttrs.getRequestId(), computedAttrs.getRequestId());
                              Assert.assertEquals(contextAttrs.getCountry(), computedAttrs.getCountry());
                              Assert.assertEquals(contextAttrs.getVisitor(), computedAttrs.getVisitor());

                              // Check that other fields *are* copied from the implicit attrs
                              Assert.assertEquals(defaultAttrs.getDeviceId(), computedAttrs.getDeviceId());
                              Assert.assertEquals(defaultAttrs.getLocales().size(), computedAttrs.getLocales().size());

                          });
    }

    private void checkExpected(final VisitContext context, final Consumer<RequestAttributes> checker) {
        manager.runWithRequestAttributes(defaultAttrs, () -> {
                                                           // Check both the merged RequestAttributes
                                                           checker.accept(helper.fromProtobuf(context));
                                                           // ... and the installation of the merge attrs as current
                                                           helper.runWithVisitContext(context, () -> checker.accept(manager.getCurrentRequestAttributes()));
                                                       });
    }

}
