package com.netflix.request.core;

import com.netflix.request.protogen.UiFlavor;
import java.util.EnumSet;
import java.util.Optional;
import org.junit.Test;
import org.junit.Assert;

public class UiFlavorsTest {

    @Test
    public void fromName_checkAllUiFlavorsFound() {
        for (final UiFlavor uiFlavor : EnumSet.allOf(UiFlavor.class)) {
            Optional<UiFlavor> foundUi = UiFlavors.fromName(uiFlavor.name()
                                                                          .toLowerCase());
            Assert.assertTrue(foundUi.isPresent());
            Assert.assertEquals(uiFlavor, foundUi.get());

            foundUi = UiFlavors.fromName(uiFlavor.name()
                                                 .toUpperCase());
            Assert.assertTrue(foundUi.isPresent());
            Assert.assertEquals(uiFlavor, foundUi.get());
        }
    }

    @Test
    public void fromName_assertEmptyOnInvalid() {
        final Optional<UiFlavor> foundUi = UiFlavors.fromName("invalid ui");
            Assert.assertFalse(foundUi.isPresent());
    }

    @Test
    public void fromNameOrDefault_checkAllUiFlavorsFound() {
        for (final UiFlavor uiFlavor : EnumSet.allOf(UiFlavor.class)) {
            UiFlavor foundUi = UiFlavors.fromNameOrDefault(uiFlavor.name()
                                                                    .toLowerCase());
            Assert.assertEquals(uiFlavor, foundUi);

            foundUi = UiFlavors.fromNameOrDefault(uiFlavor.name()
                                                 .toUpperCase());
            Assert.assertEquals(uiFlavor, foundUi);
        }
    }

    @Test
    public void fromNameOrDefault_assertUnknownOnInvalid() {
        final UiFlavor foundUi = UiFlavors.fromNameOrDefault("invalid ui");
        Assert.assertEquals(UiFlavor.UNKNOWN_UI_FLAVOR, foundUi);
    }
}