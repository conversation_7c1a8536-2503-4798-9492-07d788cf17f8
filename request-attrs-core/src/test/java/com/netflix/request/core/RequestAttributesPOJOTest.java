package com.netflix.request.core;

import com.netflix.request.RequestAttributes;
import com.netflix.request.protogen.UiFlavor;
import com.netflix.type.SimpleDeviceType;
import com.netflix.type.NFCountry;
import com.netflix.type.SimpleVisitor;

import org.junit.Assert;
import org.junit.Test;

import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

public class RequestAttributesPOJOTest {

    private static final long TIME = System.currentTimeMillis();

    @Test
    public void testEquality() {
        final RequestAttributes ra1 = makeRequestAttributes();
        final RequestAttributes ra2 = makeRequestAttributes();
        Assert.assertEquals(ra1.hashCode(), ra2.hashCode());
        Assert.assertEquals(ra1, ra2);
        Assert.assertEquals(ra1.hashCode(), ra2.hashCode());
        Assert.assertEquals(ra1, ra2);
    }

    @Test
    public void testCopying() {
        final RequestAttributes ra = makeRequestAttributes();
        final RequestAttributes copy = RequestAttributesPOJO.builder()
                                                  .copying(ra)
                                                  .build();
        Assert.assertEquals(ra.hashCode(), copy.hashCode());
        Assert.assertEquals(ra, copy);
    }

    @Test
    public void testRequestId() {
        final RequestAttributes ra = makeRequestAttributes();
        final RequestAttributes copy = RequestAttributesPOJO.builder()
                                                            .copying(ra)
                                                            .withRequestId(ra.getRequestId() + "2")
                                                            .build();
        Assert.assertNotEquals(ra.hashCode(), copy.hashCode());
        Assert.assertNotEquals(ra, copy);
    }

    @Test
    public void testTrackId() {
        final RequestAttributes ra = makeRequestAttributes();
        final RequestAttributes copy = RequestAttributesPOJO.builder()
                                                            .copying(ra)
                                                            .withTrackId(ra.getTrackId() + "2")
                                                            .build();
        Assert.assertNotEquals(ra.hashCode(), copy.hashCode());
        Assert.assertNotEquals(ra, copy);
    }

    @Test
    public void testDeviceType() {
        final RequestAttributes ra = makeRequestAttributes();
        final RequestAttributes copy = RequestAttributesPOJO.builder()
                                                            .copying(ra)
                                                            .withDeviceType(new SimpleDeviceType(ra.getDeviceType().getId() + 2))
                                                            .build();
        Assert.assertNotEquals(ra.hashCode(), copy.hashCode());
        Assert.assertNotEquals(ra, copy);
    }

    @Test
    public void testDeviceId() {
        final RequestAttributes ra = makeRequestAttributes();
        final RequestAttributes copy = RequestAttributesPOJO.builder()
                                                            .copying(ra)
                                                            .withDeviceId(ra.getDeviceId() + "_abc")
                                                            .build();
        Assert.assertNotEquals(ra.hashCode(), copy.hashCode());
        Assert.assertNotEquals(ra, copy);
    }

    @Test
    public void testUIFlavor() {
        final RequestAttributes ra = makeRequestAttributes();
        final RequestAttributes copy = RequestAttributesPOJO.builder()
                                                            .copying(ra)
                                                            .withUIFlavor(UiFlavor.AKIRA)
                                                            .build();
        Assert.assertNotEquals(ra.hashCode(), copy.hashCode());
        Assert.assertNotEquals(ra, copy);
    }

    @Test
    public void testGMTOffset() {
        final RequestAttributes ra = makeRequestAttributes();
        final RequestAttributes copy = RequestAttributesPOJO.builder()
                                                            .copying(ra)
                                                            .withGMTOffset(ra.getGMTOffset() - (int) TimeUnit.HOURS.toMillis(1))
                                                            .build();
        Assert.assertNotEquals(ra.hashCode(), copy.hashCode());
        Assert.assertNotEquals(ra, copy);
    }

    @Test
    public void testIsArtificial() {
        final RequestAttributes ra = makeRequestAttributes();
        final RequestAttributes copy = RequestAttributesPOJO.builder()
                                                            .copying(ra)
                                                            .withIsArtificial(!ra.isArtificial())
                                                            .build();
        Assert.assertNotEquals(ra.hashCode(), copy.hashCode());
        Assert.assertNotEquals(ra, copy);
    }

    @Test
    public void testIsVPNProxy() {
        final RequestAttributes ra = makeRequestAttributes();
        final RequestAttributes copy = RequestAttributesPOJO.builder()
                                                            .copying(ra)
                                                            .withIsVPNProxy(!ra.isVPNProxy())
                                                            .build();
        Assert.assertNotEquals(ra.hashCode(), copy.hashCode());
        Assert.assertNotEquals(ra, copy);
    }

    private RequestAttributes makeRequestAttributes() {
        return RequestAttributesPOJO.builder()
                                    .withRequestId("123-its-going-to-be-so-easy")
                                    .withTrackId("I'm watching you")
                                    .withDeviceType(new SimpleDeviceType(123))
                                    .withDeviceId("the id of the device")
                                    .withUIFlavor(UiFlavor.ARGO)
                                    .withCountry(NFCountry.MA)
                                    .withVisitor(new SimpleVisitor(5898912385L))
                                    .withTime(TIME)
                                    .withGMTOffset((int) TimeUnit.HOURS.toMillis(1))
                                    .withTimeZone(TimeZone.getTimeZone("GMT+1"))
                                    .withLocales("ar-MA", "fr-MA")
                                    .withContentPreviewId("bogus content preview ID")
                                    .withExtraAttribute("fred", "ethyl")
                                    .withExtraAttribute("ricky", "lucy")
                                    .withIsArtificial(true)
                                    .withIsVPNProxy(true)
                                    .build();
    }

}
