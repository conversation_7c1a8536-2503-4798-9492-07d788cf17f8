package com.netflix.request.core;

import com.netflix.request.RequestAttributes;
import com.netflix.request.RequestAttributesManager;
import com.netflix.request.core.RequestAttributesPOJO;
import com.netflix.type.NFCountry;

import org.junit.Assert;
import org.junit.Test;

import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * Holder of some test routines for {@code RequestAttributesManager} objects.
 */
public class RequestAttributesManagerTests {

    private final RequestAttributesManager manager = new RequestAttributesManagerThreadLocal();

    /*
     * Lifecycle
     */

    /**
     * Creates a new <code>RequestAttributesManagerTests</code> instance.
     */
    public RequestAttributesManagerTests() {
    }

    @Test
    public void testClosures() {
        final RequestAttributes a1 = manager.newRequestAttributesBuilder()
                                            .withCountry(NFCountry.US)
                                            .withLocales("en-US")
                                            .build();
        final RequestAttributes a2 = manager.newRequestAttributesBuilder()
                                            .withCountry(NFCountry.MX)
                                            .withLocales("ee-MX")
                                            .build();
        testRunnables(a1, a2);
        testCallables(a1, a2);
        testSuppliers(a1, a2);
    }

    private void testRunnables(final RequestAttributes a1, final RequestAttributes a2) {
        final Runnable r = () -> {
            Assert.assertEquals(a1, manager.getCurrentRequestAttributes());
            manager.runWithRequestAttributes(a2, () -> {
                                                     Assert.assertEquals(a2, manager.getCurrentRequestAttributes());
                                                 });
            Assert.assertEquals(a1, manager.getCurrentRequestAttributes());
        };
        manager.makeClosure(a1, r).run();
    }

    private void testCallables(final RequestAttributes a1, final RequestAttributes a2) {
        final Callable<Boolean> c = () -> {
            Assert.assertEquals(a1, manager.getCurrentRequestAttributes());
            manager.callWithRequestAttributes(a2, () -> {
                                                      Assert.assertEquals(a2, manager.getCurrentRequestAttributes());
                                                      return Boolean.TRUE;
                                                  });
            Assert.assertEquals(a1, manager.getCurrentRequestAttributes());
            return Boolean.TRUE;
        };
        try {
            manager.makeClosure(a1, c).call();
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void testSuppliers(final RequestAttributes a1, final RequestAttributes a2) {
        final Supplier<Boolean> s = () -> {
            Assert.assertEquals(a1, manager.getCurrentRequestAttributes());
            manager.getWithRequestAttributes(a2, () -> {
                                                      Assert.assertEquals(a2, manager.getCurrentRequestAttributes());
                                                      return Boolean.TRUE;
                                                  });
            Assert.assertEquals(a1, manager.getCurrentRequestAttributes());
            return Boolean.TRUE;
        };
        manager.makeClosure(a1, s).get();
    }

} // RequestAttributesManagerTests
