package com.netflix.request.core;

import com.google.protobuf.Descriptors.EnumValueDescriptor;
import com.netflix.request.RequestAttributes;
import com.netflix.request.RequestAttributesManager;
import com.netflix.request.VisitContextManager;
import com.netflix.request.protogen.RequestAttributesProto;
import com.netflix.request.protogen.UiFlavor;
import com.netflix.request.protogen.VisitContext;
import com.netflix.type.Visitor;
import com.netflix.type.proto.DeviceTypes;
import com.netflix.type.proto.ISOCountries;
import com.netflix.type.proto.Visitors;

import com.google.protobuf.StringValue;
import com.google.protobuf.util.Durations;
import com.google.protobuf.util.Timestamps;

import javax.annotation.Nullable;
import javax.inject.Inject;
import javax.inject.Singleton;

import java.time.ZoneOffset;
import java.util.TimeZone;
import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * Standard implementation of the {@code VisitContextManager} interface.
 */
@Singleton
public class StandardVisitContextManager implements VisitContextManager {

    private final RequestAttributesManager reqAttrManager;

    @Inject
    public StandardVisitContextManager(final RequestAttributesManager reqAttrManager) {
        this.reqAttrManager = reqAttrManager;
    }

    /*
     * VisitContextManager protocol
     */

    @Override
    public RequestAttributesProto toProtobuf(final RequestAttributes attrs) {
        RequestAttributesProto.Builder builder = RequestAttributesProto.newBuilder();
        if (attrs.getRequestId() != null) {
            builder.setRequestId(toStringValue(attrs.getRequestId()));
        }
        if (attrs.getTrackId() != null) {
            builder.setTrackId(toStringValue(attrs.getTrackId()));
        }
        if (attrs.getDeviceType() != null) {
            builder.setDeviceType(DeviceTypes.toProtobuf(attrs.getDeviceType()));
        }
        if (attrs.getDeviceId() != null) {
            builder.setDeviceId(toStringValue(attrs.getDeviceId()));
        }
        if (attrs.getUIFlavor() != null) {
            builder.setUiFlavorEnum(attrs.getUIFlavor());
        }
        if (attrs.getCountry() != null) {
            builder.setCountry(ISOCountries.toProtobuf(attrs.getCountry()));
        }
        if (attrs.getVisitor() != null) {
            builder.setVisitor(Visitors.toProtobuf(attrs.getVisitor()));
        }
        if (attrs.getTime() != null) {
            builder.setTime(Timestamps.fromMillis(attrs.getTime().longValue()));
        }
        if (attrs.getZoneOffset() != null) {
            builder.setZoneOffset(Durations.fromSeconds(attrs.getZoneOffset().getTotalSeconds()));
        }
        if (attrs.getTimeZone() != null) {
            builder.setTimeZone(toStringValue(attrs.getTimeZone().getID()));
        }
        builder.setLocales(RequestAttributesProto.LocaleList.newBuilder()
                                                            .addAllLocales(attrs.getLocales())
                                                            .build());
        if (attrs.getContentPreviewId() != null) {
            builder.setContentPreviewId(toStringValue(attrs.getContentPreviewId()));
        }
        builder.setExtraAttributes(RequestAttributesProto.ExtraAttributes.newBuilder()
                                                                         .putAllExtraAttributes(attrs.getExtraAttributes())
                                   .build());
        if (attrs.isArtificial()) {
            builder.setIsArtificial(true);
        }
        if (attrs.isVPNProxy()) {
            builder.setIsVPNProxy(true);
        }
        return builder.build();
    }

    private final StringValue toStringValue(final String s) {
        return StringValue.newBuilder()
                          .setValue(s)
                          .build();
    }

    @Override
    public RequestAttributes fromProtobuf(final RequestAttributesProto proto) {
        return fillBuilderFromProtobuf(proto, reqAttrManager.newRequestAttributesBuilder()).build();
    }

    private RequestAttributes.Builder fillBuilderFromProtobuf(final RequestAttributesProto proto, final RequestAttributes.Builder builder) {
        if (proto.hasRequestId()) {
            builder.withRequestId(proto.getRequestId().getValue());
        }
        if (proto.hasTrackId()) {
            builder.withTrackId(proto.getTrackId().getValue());
        }
        if (proto.hasDeviceType()) {
            builder.withDeviceType(DeviceTypes.toBasicType(proto.getDeviceType()));
        }
        if (proto.hasDeviceId()) {
            builder.withDeviceId(proto.getDeviceId().getValue());
        }
        builder.withUIFlavor(proto.getUiFlavorEnum());
        if (proto.hasCountry()) {
            builder.withCountry(ISOCountries.toBasicType(proto.getCountry()));
        }
        if (proto.hasVisitor()) {
            builder.withVisitor(Visitors.toBasicType(proto.getVisitor()));
        }
        if (proto.hasTime()) {
            builder.withTime(Timestamps.toMillis(proto.getTime()));
        }
        if (proto.hasZoneOffset()) {
            builder.withZoneOffset(ZoneOffset.ofTotalSeconds((int) Durations.toSeconds(proto.getZoneOffset())));
        }
        if (proto.hasTimeZone()) {
            builder.withTimeZone(TimeZone.getTimeZone(proto.getTimeZone().getValue()));
        }
        if (proto.hasLocales()) {
            builder.withLocales(proto.getLocales().getLocalesList());
        }
        if (proto.hasContentPreviewId()) {
            builder.withContentPreviewId(proto.getContentPreviewId().getValue());
        }
        if (proto.hasExtraAttributes()) {
            builder.withExtraAttributes(proto.getExtraAttributes().getExtraAttributesMap());
        }
        if (proto.hasIsArtificial()) {
            builder.withIsArtificial(proto.getIsArtificial().getValue());
        }
        if (proto.hasIsVPNProxy()) {
            builder.withIsVPNProxy(proto.getIsVPNProxy().getValue());
        }
        return builder;
    }

    @Override
    public RequestAttributes fromProtobuf(final VisitContext context) {
        switch (context.getUsage()) {
        case USE_IMPLICIT:
            return reqAttrManager.getCurrentRequestAttributes();

        case IGNORE_IMPLICIT:
            if (context.hasRequestAttributes()) {
                return fromProtobuf(context.getRequestAttributes());
            } else {
                throw new NullPointerException("VisitContext with IGNORE_IMPLICIT usage must contain a RequestAttributes");
            }

        case EXTEND_IMPLICIT:
            if (context.hasRequestAttributes()) {
                final RequestAttributes.Builder builder = reqAttrManager.newRequestAttributesBuilder();
                builder.copying(reqAttrManager.getCurrentRequestAttributes());
                fillBuilderFromProtobuf(context.getRequestAttributes(), builder);
                return builder.build();
            } else {
                return reqAttrManager.getCurrentRequestAttributes();
            }

        default:
            throw new IllegalArgumentException("Unknown VisitContext Usage: " + context.getUsage());
        }
    }

    @Override
    public VisitContext makeImplicitVisitContext() {
        return makeImplicitVisitContext(reqAttrManager.getCurrentRequestAttributes().getVisitor());
    }

    @Override
    public VisitContext makeImplicitVisitContext(final Visitor visitor) {
        return VisitContext.newBuilder()
                           .setUsage(VisitContext.Usage.EXTEND_IMPLICIT)
                           .setRequestAttributes(RequestAttributesProto.newBuilder()
                                                                       .setVisitor(Visitors.toProtobuf(visitor))
                                                                       .build())
                           .build();
    }

    @Override
    public VisitContext makeExplicitVisitContext() {
        return makeExplicitVisitContext(reqAttrManager.getCurrentRequestAttributes());
    }

    @Override
    public VisitContext makeExplicitVisitContext(final RequestAttributes reqAttrs) {
        return VisitContext.newBuilder()
                           .setUsage(VisitContext.Usage.IGNORE_IMPLICIT)
                           .setRequestAttributes(toProtobuf(reqAttrManager.getCurrentRequestAttributes()))
                           .build();
    }

    @Override
    public <T> T callWithVisitContext(final VisitContext context, final Callable<T> callable) throws Exception {
        final RequestAttributes attrs = getRequestAttributesOverride(context);
        if (attrs != null) {
            return reqAttrManager.callWithRequestAttributes(attrs, callable);
        } else {
            return callable.call();
        }
    }

    @Override
    public <T> T getWithVisitContext(final VisitContext context, final Supplier<T> supplier) {
        final RequestAttributes attrs = getRequestAttributesOverride(context);
        if (attrs != null) {
            return reqAttrManager.getWithRequestAttributes(attrs, supplier);
        } else {
            return supplier.get();
        }
    }

    @Override
    public void runWithVisitContext(final VisitContext context, final Runnable runnable) {
        final RequestAttributes attrs = getRequestAttributesOverride(context);
        if (attrs != null) {
            reqAttrManager.runWithRequestAttributes(attrs, runnable);
        } else {
            runnable.run();
        }
    }

    /**
     * Gets the RequestAttributes to use, as specified by the given VisitContext.
     * @return null to use the current value, without change
     */
    @Nullable
    private RequestAttributes getRequestAttributesOverride(final VisitContext context) {
        if (context != null) {
            switch (context.getUsage()) {
            case USE_IMPLICIT:
                // Fall through to use current RequestAttributes
                break;

            case IGNORE_IMPLICIT:
                return fromProtobuf(context);

            case EXTEND_IMPLICIT:
                if (context.hasRequestAttributes()) {
                    return fromProtobuf(context);
                }
                // Fall through to use current RequestAttributes
                break;

            default:
                throw new IllegalArgumentException("Unknown VisitContext Usage: " + context.getUsage());
            }
        }
        // Fall through to use current RequestAttributes

        if (reqAttrManager.hasCurrentRequestAttributes()) {
            return null;
        } else {
            // No current context, so create an empty one to use.
            return reqAttrManager.newRequestAttributesBuilder().build();
        }
    }

 } // StandardVisitContextManager
