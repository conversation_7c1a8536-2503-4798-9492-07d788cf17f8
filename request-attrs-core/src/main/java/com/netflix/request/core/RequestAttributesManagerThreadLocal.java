package com.netflix.request.core;

import com.netflix.request.RequestAttributes;

import javax.inject.Inject;
import javax.inject.Singleton;

import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * Implementation of the {@code RequestAttributesManager} interface that uses a
 * {@code InheritableThreadLocal} variable to hold the "current" {@code RequestAttributes} object.
 * This is intended for use in testing.
 * Production code deployed in the standard Netflix stack should use the
 * {@code RequestAttributesManagerBaseServer} implementation.
 */
@Singleton
public class RequestAttributesManagerThreadLocal extends AbstractRequestAttributesManager {

    private final ThreadLocal<RequestAttributes> currentRequestAttrs = new InheritableThreadLocal<>();

    /*
     * Lifecycle
     */

    /**
     * Creates a new <code>RequestAttributesManagerThreadLocal</code> instance.
     */
    @Inject
    public RequestAttributesManagerThreadLocal() {
        super();
    }

    /*
     * RequestAttributesManager protocol
     */

    @Override
    public RequestAttributes.Builder newRequestAttributesBuilder() {
        return RequestAttributesPOJO.builder();
    }

    @Override
    public RequestAttributes getCurrentRequestAttributes() {
        if (!hasCurrentRequestAttributes()) {
            throw new IllegalStateException("No current RequestAttributes are bound");
        }
        return currentRequestAttrs.get();
    }

    @Override
    public boolean hasCurrentRequestAttributes() {
        return currentRequestAttrs.get() != null;
    }

    @Override
    public <T> T callWithRequestAttributes(final RequestAttributes attrs, final Callable<T> callable) throws Exception {
        final RequestAttributes orig = currentRequestAttrs.get();
        try {
            currentRequestAttrs.set(attrs);
            return callable.call();
        } finally {
            currentRequestAttrs.set(orig);
        }
    }

    @Override
    public void runWithRequestAttributes(final RequestAttributes attrs, final Runnable runnable) {
        final RequestAttributes orig = currentRequestAttrs.get();
        try {
            currentRequestAttrs.set(attrs);
            runnable.run();
        } finally {
            currentRequestAttrs.set(orig);
        }
    }

    @Override
    public <T> T getWithRequestAttributes(final RequestAttributes attrs, final Supplier<T> supplier) {
        final RequestAttributes orig = currentRequestAttrs.get();
        try {
            currentRequestAttrs.set(attrs);
            return supplier.get();
        } finally {
            currentRequestAttrs.set(orig);
        }
    }

} // RequestAttributesManagerThreadLocal
