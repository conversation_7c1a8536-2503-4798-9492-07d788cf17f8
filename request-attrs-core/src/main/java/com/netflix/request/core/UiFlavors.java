package com.netflix.request.core;

import static java.util.Objects.requireNonNull;

import com.netflix.request.protogen.UiFlavor;
import java.util.EnumSet;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * A utility over the enum {@link UiFlavor}.
 */
public final class UiFlavors {

    @SuppressWarnings("Convert2MethodRef")
    private static final Map<String, UiFlavor> UI_FLAVORS = EnumSet.allOf(UiFlavor.class)
                                                                   .stream()
                                                                   .collect(Collectors.toMap(uiFlavor -> uiFlavor.name(), Function.identity()));

    private UiFlavors() { }

    /**
     * Given the enum name of the UI Flavor, ignoring case, will return the matching {@link UiFlavor}.
     * @param name The string name of the the UI Flavor.
     * @return The matching {@link UiFlavor} or {@link Optional#empty()}.
     */
    public static Optional<UiFlavor> fromName(final String name) {
        final UiFlavor value = UI_FLAVORS.get(requireNonNull(name, "Name should not be null."));
        if (value != null) {
            return Optional.of(value);
        }

        return UI_FLAVORS.entrySet()
                         .stream()
                         .filter(e -> e.getKey().equalsIgnoreCase(name))
                         .findFirst()
                         .map(Entry::getValue);
    }

    /**
     * Given the enum name of the UI Flavor, ignoring case, will return the matching {@link UiFlavor} or if the
     * UI Flavor is not found will return {@link UiFlavor#UNKNOWN_UI_FLAVOR}.
     * @param name The string name of the the UI Flavor.
     * @return The matching {@link UiFlavor} or {@link UiFlavor#UNKNOWN_UI_FLAVOR}.
     */
    public static UiFlavor fromNameOrDefault(final String name) {
        return fromName(name).orElse(UiFlavor.UNKNOWN_UI_FLAVOR);
    }
}
