package com.netflix.request.core;

import com.netflix.request.RequestAttributes;
import com.netflix.request.protogen.UiFlavor;
import com.netflix.type.DeviceType;
import com.netflix.type.ISOCountry;
import com.netflix.type.SerializableDeviceType;
import com.netflix.type.SerializableISOCountry;
import com.netflix.type.SerializableVisitor;
import com.netflix.type.Visitor;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;

/**
 * A simple POJO implementation of the {@code RequestAttributes} interface.
 */
public final class RequestAttributesPOJO extends AbstractRequestAttributes {

    /**
     * A Builder for the POJO.
     */
    private static final class RABuilder extends AbstractRequestAttributes.AbstractBuilder {

        private final RequestAttributesPOJO pojo = new RequestAttributesPOJO();
        private boolean isFinal = false;

        private RABuilder() {
            super();
        }

        @Override
        public Builder withRequestId(final String id) {
            checkIsActive();
            pojo.requestId = id;
            return this;
        }

        @Override
        public Builder withTrackId(final String id) {
            checkIsActive();
            pojo.trackId = id;
            return this;
        }

        @Override
        public Builder withDeviceType(final DeviceType deviceType) {
            checkIsActive();
            pojo.deviceType = SerializableDeviceType.wrap(deviceType);
            return this;
        }

        @Override
        public Builder withDeviceId(final String id) {
            checkIsActive();
            pojo.deviceId = id;
            return this;
        }

        @Override
        public Builder withUIFlavor(final UiFlavor flavor) {
            checkIsActive();
            pojo.uiFlavor = flavor;
            return this;
        }

        @Override
        public Builder withCountry(final ISOCountry country) {
            checkIsActive();
            pojo.country = SerializableISOCountry.wrap(country);
            return this;
        }

        @Override
        public Builder withVisitor(final Visitor visitor) {
            checkIsActive();
            pojo.visitor = SerializableVisitor.wrap(visitor);
            return this;
        }

        @Override
        public Builder withTime(final Long millis) {
            return withInstant((millis == null) ? null : Instant.ofEpochMilli(millis));
        }

        @Override
        public Builder withInstant(final Instant instant) {
            checkIsActive();
            pojo.time = instant;
            return this;
        }

        @Override
        public Builder withZoneOffset(final ZoneOffset offset) {
            checkIsActive();
            pojo.zoneOffset = offset;
            return this;
        }

        @Override
        public Builder withTimeZone(final TimeZone tz) {
            checkIsActive();
            pojo.timeZone = tz;
            return this;
        }

        @Override
        public Builder withLocales(final List<String> locales) {
            checkIsActive();
            pojo.locales.clear();
            pojo.locales.addAll(locales);
            return this;
        }

        @Override
        public Builder withLocales(final String... locales) {
            checkIsActive();
            pojo.locales.clear();
            for (String l : locales) {
                pojo.locales.add(l);
            }
            return this;
        }

        @Override
        public Builder addingLocales(final String... locales) {
            checkIsActive();
            for (String l : locales) {
                pojo.locales.add(l);
            }
            return this;
        }

        @Override
        public Builder withContentPreviewId(final String id) {
            checkIsActive();
            pojo.contentPreviewId = id;
            return this;
        }

        @Override
        public Builder withExtraAttribute(final String name, final String value) {
            checkIsActive();
            pojo.extraAttributes.put(name, value);
            return this;
        }

        @Override
        public Builder withExtraAttributes(final Map<String, String> attributes) {
            checkIsActive();
            pojo.extraAttributes.clear();
            pojo.extraAttributes.putAll(attributes);
            return this;
        }

        @Override
        public Builder withIsArtificial(final boolean flag) {
            checkIsActive();
            pojo.isArtificial = flag;
            return this;
        }

        @Override
        public Builder withIsVPNProxy(final boolean flag) {
            checkIsActive();
            pojo.isVPNProxy = flag;
            return this;
        }

        @Override
        public Builder withIsNonMember(final boolean flag) {
            checkIsActive();
            pojo.isNonMember = flag;
            return this;
        }

        @Override
        public RequestAttributes build() {
            if (isFinal) {
                throw new IllegalStateException();
            }
            isFinal = true;
            pojo.locales = Collections.unmodifiableList(pojo.locales);
            pojo.locale = pojo.locales.stream()
                                      .findFirst()
                                      .orElse(null);
            pojo.extraAttributes = Collections.unmodifiableMap(pojo.extraAttributes);
            return pojo;
        }

        private void checkIsActive() {
            if (isFinal) {
                throw new IllegalStateException("Attempt to use Builder after object has been built.");
            }
        }
    } // RABuilder

    private static final long serialVersionUID = 4813721964315831558L;

    private String requestId = null;
    private String trackId = null;
    private SerializableDeviceType deviceType = null;
    private String deviceId = null;
    private UiFlavor uiFlavor = UiFlavor.UNKNOWN_UI_FLAVOR;
    private SerializableISOCountry country = null;
    private SerializableVisitor visitor = null;
    private Instant time = null;
    private ZoneOffset zoneOffset = null;
    private TimeZone timeZone = null;
    private List<String> locales = new ArrayList<>();
    private String locale = null;
    private String contentPreviewId = null;
    private Map<String, String> extraAttributes = new HashMap<>();
    private boolean isArtificial = false;
    private boolean isVPNProxy = false;
    private boolean isNonMember = false;

    /*
     * Lifecycle
     */

    /**
     * Creates a new builder.
     */
    public static RequestAttributes.Builder builder() {
        return new RABuilder();
    }

    /**
     * Creates a new <code>RequestAttributesPOJO</code> instance.
     */
    private RequestAttributesPOJO() {
    }

    /*
     * RequestAttributes protocol
     */

    @Override
    public String getRequestId() {
        return requestId;
    }

    @Override
    public String getTrackId() {
        return trackId;
    }

    @Override
    @edu.umd.cs.findbugs.annotations.SuppressFBWarnings("EI_EXPOSE_REP")
    public DeviceType getDeviceType() {
        return deviceType;
    }

    @Override
    public String getDeviceId() {
        return deviceId;
    }

    @Override
    public UiFlavor getUIFlavor() {
        return uiFlavor;
    }

    @Override
    @edu.umd.cs.findbugs.annotations.SuppressFBWarnings("EI_EXPOSE_REP")
    public ISOCountry getCountry() {
        return country;
    }

    @Override
    @edu.umd.cs.findbugs.annotations.SuppressFBWarnings("EI_EXPOSE_REP")
    public Visitor getVisitor() {
        return visitor;
    }

    @Override
    public Long getTime() {
        return (time == null) ? null : Long.valueOf(time.toEpochMilli());
    }

    @Override
    public Instant getInstant() {
        return time;
    }

    @Override
    public ZoneOffset getZoneOffset() {
        return zoneOffset;
    }

    @Override
    @edu.umd.cs.findbugs.annotations.SuppressFBWarnings("EI_EXPOSE_REP")
    public TimeZone getTimeZone() {
        return timeZone;
    }

    @Override
    @edu.umd.cs.findbugs.annotations.SuppressFBWarnings("EI_EXPOSE_REP")
    public List<String> getLocales() {
        return locales;
    }

    @Override
    public String getLocale() {
        /*
         * Very minor efficiency hack: most locale related fetches are for the primary locale,
         * so cache it ast create time rather that fetching from the list each time.
         */
        return locale;
    }

    @Override
    public String getContentPreviewId() {
        return contentPreviewId;
    }

    @Override
    @edu.umd.cs.findbugs.annotations.SuppressFBWarnings("EI_EXPOSE_REP")
    public Map<String, String> getExtraAttributes() {
        return extraAttributes;
    }

    @Override
    public Optional<String> getExtraAttribute(final String name) {
        return Optional.ofNullable(extraAttributes.get(name));
    }

    @Override
    public boolean isArtificial() {
        return isArtificial;
    }

    @Override
    public boolean isVPNProxy() {
        return isVPNProxy;
    }

    @Override
    public boolean isNonMember() {
        return isNonMember;
    }
} // RequestAttributesPOJO
