package com.netflix.request.core;

import com.netflix.request.RequestAttributes;
import com.netflix.type.IdObject;

import java.time.Instant;
import java.time.ZoneOffset;
import java.util.Objects;
import java.util.TimeZone;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Partial implementation of the {@code RequestAttributes} interface that
 * enforces equality constraints on objects.
 */
@SuppressWarnings("serial")
public abstract class AbstractRequestAttributes implements RequestAttributes {

    /**
     * A partial implementation of the {@code RequestAttributes.Builder} interface.
     */
    protected abstract static class AbstractBuilder implements RequestAttributes.Builder {

        private static final String UUID_PREFIX = UUID.randomUUID().toString() + "-";
        private static final AtomicLong UUID_COUNT = new AtomicLong();

        protected AbstractBuilder() { }

        @Override
        public Builder copying(final RequestAttributes reqAttrs) {
            return withRequestId(reqAttrs.getRequestId())
                .withTrackId(reqAttrs.getTrackId())
                .withDeviceType(reqAttrs.getDeviceType())
                .withDeviceId(reqAttrs.getDeviceId())
                .withCountry(reqAttrs.getCountry())
                .withVisitor(reqAttrs.getVisitor())
                .withTime(reqAttrs.getTime())
                .withZoneOffset(reqAttrs.getZoneOffset())
                .withTimeZone(reqAttrs.getTimeZone())
                .withLocales(reqAttrs.getLocales())
                .withContentPreviewId(reqAttrs.getContentPreviewId())
                .withExtraAttributes(reqAttrs.getExtraAttributes())
                .withIsArtificial(reqAttrs.isArtificial())
                .withIsVPNProxy(reqAttrs.isVPNProxy())
                .withUIFlavor(reqAttrs.getUIFlavor())
                .withIsNonMember(reqAttrs.isNonMember());
        }

        /*
         * RequestAttributes.Builder protocol
         */

        @Override
        public Builder withNewRequestId() {
            return withRequestId(UUID_PREFIX + UUID_COUNT.getAndIncrement());
        }

        @Override
        public Builder withGMTOffset(final Integer offset) {
            return withZoneOffset((offset == null) ? null
                                                   : ZoneOffset.ofTotalSeconds((int) TimeUnit.MILLISECONDS.toSeconds(offset)));
        }

    } // AbstractBuilder

    private final Instant createTime = Instant.now();

    /*
     * Lifecycle
     */

    /**
     * Creates a new <code>AbstractRequestAttributes</code> instance.
     */
    protected AbstractRequestAttributes() {
    }

    /*
     * RequestAttributes protocol
     */

    @Override
    public long getEffectiveTime() {
        return getEffectiveInstant().toEpochMilli();
    }

    @Override
    public Instant getEffectiveInstant() {
        final Instant time = getInstant();
        return (time == null) ? createTime : time;
    }

    @Override
    public Integer getGMTOffset() {
        final ZoneOffset offset = getZoneOffset();
        return (offset == null) ? null : (int) TimeUnit.SECONDS.toMillis(offset.getTotalSeconds());
    }

    @Override
    public String getLocale() {
        return getLocales().stream().findFirst().orElse(null);
    }

    /*
     * Object protocol
     */

    @Override
    public int hashCode() {
        return Objects.hash(getRequestId(),
                            getTrackId(),
                            getDeviceType(),
                            getDeviceId(),
                            getUIFlavor(),
                            getCountry(),
                            getVisitor(),
                            getTime(),
                            getZoneOffset(),
                            getTimeZone(),
                            getLocales(),
                            getContentPreviewId(),
                            getExtraAttributes(),
                            isArtificial(),
                            isVPNProxy(),
                            isNonMember());
    }

    @Override
    public boolean equals(final Object o) {
        if (o instanceof RequestAttributes) {
            final RequestAttributes other = (RequestAttributes) o;
            return Objects.equals(getRequestId(), other.getRequestId())
                && Objects.equals(getTrackId(), other.getTrackId())
                && Objects.equals(getDeviceType(), other.getDeviceType())
                && Objects.equals(getDeviceId(), other.getDeviceId())
                && Objects.equals(getUIFlavor(), other.getUIFlavor())
                && Objects.equals(getCountry(), other.getCountry())
                && Objects.equals(getVisitor(), other.getVisitor())
                && Objects.equals(getTime(), other.getTime())
                && Objects.equals(getZoneOffset(), other.getZoneOffset())
                && Objects.equals(getTimeZone(), other.getTimeZone())
                && Objects.equals(getLocales(), other.getLocales())
                && Objects.equals(getContentPreviewId(), other.getContentPreviewId())
                && Objects.equals(getExtraAttributes(), other.getExtraAttributes())
                && Objects.equals(isArtificial(), other.isArtificial())
                && Objects.equals(isVPNProxy(), other.isVPNProxy())
                && Objects.equals(isNonMember(), other.isNonMember());
        } else {
            return false;
        }
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{RequestAttributes:");
        toString(sb, "requestId", getRequestId());
        toString(sb, "trackId", getTrackId());
        toString(sb, "deviceType", getDeviceType());
        toString(sb, "deviceId", getDeviceId());
        toString(sb, "uiFlavor", getUIFlavor());
        toString(sb, "country", getCountry());
        toString(sb, "visitor", getVisitor());
        toString(sb, "time", getTime());
        toString(sb, "zoneOffset", getZoneOffset());
        toString(sb, "timeZone", getTimeZone());
        toString(sb, "locales", getLocales());
        toString(sb, "contentPreviewId", getContentPreviewId());
        toString(sb, "extraAttributes", getExtraAttributes());
        if (isArtificial()) {
            toString(sb, "isArtificial", Boolean.TRUE);
        }
        if (isVPNProxy()) {
            toString(sb, "isVPNProxy", Boolean.TRUE);
        }
        return sb.append("}").toString();
    }

    private void toString(final StringBuilder sb, final String key, final Object value) {
        if (value == null) {
            // Skip it
        } else if (value instanceof IdObject) {
            toString(sb, key, ((IdObject) value).getId());
        } else if (value instanceof String) {
            sb.append(" ").append(key).append("=\"").append(value).append("\"");
        } else if (value instanceof TimeZone) {
            toString(sb, key, ((TimeZone) value).getID());
        } else {
            sb.append(" ").append(key).append("=").append(value);
        }
    }
} // AbstractRequestAttributes
