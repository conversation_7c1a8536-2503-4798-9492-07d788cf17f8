package com.netflix.request.core;

import com.netflix.request.RequestAttributes;
import com.netflix.request.RequestAttributesManager;
import com.netflix.type.Visitor;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Partial implementation of the {@code RequestAttributesManager} interface.
 */
public abstract class AbstractRequestAttributesManager implements RequestAttributesManager {

    /*
     * Lifecycle
     */

    /**
     * Creates a new <code>AbstractRequestAttributesManager</code> instance.
     */
    protected AbstractRequestAttributesManager() {
    }

    /*
     * RequestAttributesManager protocol
     */

    @Override
    public <T> T callWithVisitor(final Visitor visitor, final Callable<T> callable) throws Exception {
        return callWithRequestAttributes(attrsWithVisitor(visitor), callable);
    }

    @Override
    public void runWithVisitor(final Visitor visitor, final Runnable runnable) {
        runWithRequestAttributes(attrsWithVisitor(visitor), runnable);
    }

    @Override
    public <T> T getWithVisitor(final Visitor visitor, final Supplier<T> supplier) {
        return getWithRequestAttributes(attrsWithVisitor(visitor), supplier);
    }

    private RequestAttributes attrsWithVisitor(final Visitor visitor) {
        return newRequestAttributesBuilder().copying(getCurrentRequestAttributes())
                                            .withVisitor(visitor)
                                            .build();
    }

    @Override
    public Executor wrap(final Executor delegate) {
        return new Executor() {
            @Override
            public void execute(final Runnable command) {
                delegate.execute(makeClosure(command));
            }
        };
    }

    @Override
    public ExecutorService wrap(final ExecutorService delegate) {
        return new ExecutorService() {
            @Override
            public void shutdown() {
                delegate.shutdown();
            }
            @Override
            public List<Runnable> shutdownNow() {
                return delegate.shutdownNow();
            }
            @Override
            public boolean isShutdown() {
                return delegate.isShutdown();
            }
            @Override
            public boolean isTerminated() {
                return delegate.isTerminated();
            }
            @Override
            public boolean awaitTermination(final long timeout,
                                            final TimeUnit unit) throws InterruptedException {
                return delegate.awaitTermination(timeout, unit);
            }
            @Override
            public <T> Future<T> submit(final Callable<T> task) {
                return delegate.submit(makeClosure(task));
            }
            @Override
            public <T> Future<T> submit(final Runnable task, final T result) {
                return delegate.submit(makeClosure(task), result);
            }
            @Override
            public Future<?> submit(final Runnable task) {
                return delegate.submit(makeClosure(task));
            }
            @Override
            public <T> List<Future<T>> invokeAll(final Collection<? extends Callable<T>> tasks) throws InterruptedException {
                return delegate.invokeAll(tasks.stream()
                                               .map(AbstractRequestAttributesManager.this::makeClosure)
                                               .collect(Collectors.toList()));
            }
            @Override
            public <T> List<Future<T>> invokeAll(final Collection<? extends Callable<T>> tasks,
                                                 final long timeout,
                                                 final TimeUnit unit) throws InterruptedException {
                return delegate.invokeAll(tasks.stream()
                                               .map(AbstractRequestAttributesManager.this::makeClosure)
                                               .collect(Collectors.toList()),
                                          timeout,
                                          unit);
            }
            @Override
            public <T> T invokeAny(final Collection<? extends Callable<T>> tasks) throws InterruptedException, ExecutionException {
                return delegate.invokeAny(tasks.stream()
                                               .map(AbstractRequestAttributesManager.this::makeClosure)
                                               .collect(Collectors.toList()));
            }
            @Override
            public <T> T invokeAny(final Collection<? extends Callable<T>> tasks,
                                   final long timeout,
                                   final TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
                return delegate.invokeAny(tasks.stream()
                                               .map(AbstractRequestAttributesManager.this::makeClosure)
                                               .collect(Collectors.toList()),
                                          timeout,
                                          unit);
            }
            @Override
            public void execute(final Runnable command) {
                delegate.execute(makeClosure(command));
            }
        };
    }

} // AbstractRequestAttributesManager
