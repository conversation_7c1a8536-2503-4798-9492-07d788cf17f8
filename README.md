Request Attributes
===================================

A cleaner interface for handling request contextual data.

## Background

We often run A/B test different experiences that depend on contextual data about a user's visit
(e.g. customer ID, country of origin, type of device, timezone, even time-of-day).
Contextual data is also used to support [distributed tracing](http://go/distributed-tracing),
[request based logging](http://insight-docs.prod.netflix.net/ulog/introduction/),
and integration testing.

One key property of these use-cases is that they happen throughout the code stack, across different services.
That means that the context needs to be passed between clients and services,
and needs to be available within libraries used within a single service.
Instead of passing the context every explicitly,
we have chosen to manage it implicitly.
There are two parts to this:
making it available within a single application
and passing the context along from client to server.

Within an application context data is held in a
[`RequestContext`](https://stash.corp.netflix.com/projects/CPIE/repos/platform-legacy/browse/family/server-context/src/main/java/com/netflix/server/context)
object.
That object is stored in a
[request-scoped variable](https://stash.corp.netflix.com/projects/CPIE/repos/platform-legacy/browse/family/server-context/src/main/java/com/netflix/lang),
accessed via the `CurrentRequestContext`.
That means that code can always access the current context data,
even if it isn't supplied explicitly.

When a client makes a call to another service using the Netflix base-server code
(or gRPC with the standard Netflix gRPC extensions)
this context data is automatically included in an out-of-band channel.
The server side code (via the Netflix base-server or gRPC extensions)
extracts this data and installs it into the `CurrentRequestContext`.

This all works, but there are costs associated with those benefits.

* It's magic, and magic is hard to understand.
* There are no interfaces, only classes, and those are often static,
  so testing can be difficult.
* The implementations are crufty and _old_ (they go back to the earliest uses of Java at Netflix).
* Managing the request-scope in multi-threaded applications can be challenging.
  The implicit context has to be bundled together into the code that is run in a different thread.
* Passing out-of-band arguments to a service (e.g. via `curl`) is (to say the least) difficult.


## Request Attributes

This package addresses many of those problems.

* The Java interface `RequestAttributes` that can be used instead of the `RequestContext`.
* Injection friendly access to the current request data via `RequestAttributesManager`
  instead of using the static classes `BindingContexts`, `CurrentRequestContext`, and `CurrentVisitor`.
* Support for "time travel" by including an explicit time-of-request in the `RequestAttributes`.
* Improved efficiency due to reduced synchronization. (YMMV)
* Simple way to pass contextual info explicitly via `VisitContextManager`.

It does *not* support:

* Services that modify the current `RequestContext`.
* Some other esoteric(?) uses of `RequestContext`.


### Usage

[`RequestAttributes`](request-attrs-api/src/main/java/com/netflix/request/RequestAttributes.java)
can be used to replace most uses of `RequestContext`.
The "current" `RequestAttributes` can be obtained by using (an injected instance of)
[`RequestAttributesManager`](request-attrs-api/src/main/java/com/netflix/request/RequestAttributesManager.java).

#### Converting to RequestAttributes

| Old | New |
|-----|-----|
| `RequestContext` | `RequestAttribute` |
| `CurrentRequestContext::get()` | `RequestAttributesManager::getCurrentRequestAttributes()` |
| `CurrentVisitor::get()` | `RequestAttributesManager::getCurrentRequestAttributes().getVisitor()` |
| `CurrentRequestContext::set(RequestContext)` | `RequestAttributesManager::callWithRequestAttributes(RequestAttributes,Callable)` |
| `CurrentVisitor::set(Visitor)` | `RequestAttributesManager::callWithVisitor(Visitor,Callable)` |
| Modifying the current `RequestContext` | `RequestAttributes.Builder::copying(...).withXXX()...` |
| `BindingContexts::isInContext()` | `RequestAttributesManager::hasCurrentRequestAttributes()` |
| `BindingContexts::propagate(Executor)` | `RequestAttributesManager::wrap(Executor)` |
| `BindingContexts::propagate(ExecutorService)` | `RequestAttributesManager::wrap(ExecutorService)` |

#### Example: Fetching the current country

``` java
import com.netflix.request.RequestAttributes;
import com.netflix.request.RequestAttributesManager;
import com.netflix.type.ISOCountry;

class MyClass {

    private final RequestAttributesManager reqAttrsManager;

    @Inject
    MyClass(final RequestAttributesManager reqAttrsManager) {
        this.reqAttrsManager = reqAttrsManager;
    }

    // ...

    void doSomething() {
        final RequestAttributes reqAttrs = reqAttrsManager.getCurrentRequestAttributes();
        final ISOCountry country = reqAttrs.getCountry();
        // ...
    }
}
```

#### Example: Setting the current RequestAttributes

You don't set the current `RequestAttributes` directly.
Instead you create a closure around your code that,
when run,
will have that the current `RequestAttributes` bound as desired.

``` java
import com.netflix.request.RequestAttributes;
import com.netflix.request.RequestAttributesManager;

class MyObject {

    private final RequestAttributesManager reqAttrsManager;

    @Inject
    MyObject(final RequestAttributesManager reqAttrsManager) {
        this.reqAttrsManager = reqAttrsManager;
    }

    // ...

    public void doSomethingInContext(final RequestAttributes attrs) {
        reqAttrsManager.runWithRequestAttributes(attrs, this::doSomething);
    }

    private void doSomething() {
        // ...
    }
}
```

#### Explicit Passing of Context

For reasons mentioned above services may want to allow explicit passing of the context.
There is then the problem of reconciling the explicit context with the base-server supplied context.
The `VisitContext` class,
together with the helper class `VisitContextManager`,
support passing of the context.
The idea is that applications include a `VisitContext` field in their request structure and,
on the server side, install that context.
The context is optional:
leaving it unset is equivalent to using the automatically passed current context.
That means that most clients need not deal with contexts at all.

(`VisitContext` is a Protobuf structure, and so can be used both in gRPC and
(via Protobof support for JSON) in base-server apps.)


##### Example

```
// Protobuf definitions

import "com/netflix/request/request_attributes.proto";

message GetFooRequest {
  .com.netflix.request.VisitContext visit_context = 1;
  // ... more fields here...
}
```

``` java
/**
 * Implementation of the Foo service.
 */
public class FooServiceImpl extends FooServiceGrpc.FooServiceImplBase {
    @Inject
    VisitContextManager visitContextManager;
    // ...
    @Override
    public void getFoo(GetFooRequest request, StreamObserver<GetFooReply> responseObserver) {
        visitContextManager.runWithVisitContext(request.getVisitContext(),
                                                () -> getFooInContext(request, responseObserver));
    }
    private void getFooInContext(GetFooRequest request, StreamObserver<GetFooReply> responseObserver) {
        // do the actual work here, knowing it happens with the context set correctly
    }
}
```


### Libraries

#### request-attrs-api

Defines the Java interfaces and Protobuf types.
No other implementations are supplied.
Client code should be compiled against this library
without reference to any of the other Request Attributes libraries.

In your `build.gradle` file, include
``` groovy
dependencies {
    compile 'com.netflix.request:request-attrs-api:latest.release'
}
```

#### request-attrs-core

Client code will probably not need to refer to this library directly,
but it may be useful in testing.
It defines a simple POJO implementation of the `RequestAttributes` interface,
along with an implementation of `RequestAttributesManager` that uses a Java `ThreadLocal`
to store the current `RequestAttributes`.

#### request-attrs-base-server

This library supplies code that acts as a two-way bridge between Netflix base-server
`RequestContext` and `RequestAttributes`.
It includes an implementation of `RequestAttributesManager` that inter-operates with the base-server class `CurrentRequestContext`.
Fetching the current `RequestAttributes` creates and caches (at the current request scope) a new object populated with data from the current `RequestContext`;
binding the current `RequestAttributes` also binds a new `RequestContext` populated from the `RequestAttributes`.

#### request-attrs-lifecycle

Supplies bindings of the interfaces to the implementations defined in request-attrs-base-server.

In your application `build.gradle` file, include a compile-time depencency on the library:
``` groovy
dependencies {
    compile 'com.netflix.request:request-attrs-lifecycle:latest.release'
}
```

Note: Libraries should not include this library.
That should be left up to applications.

##### Guice

In your application Guice module, install `RequestAttributesModule`:
``` java
import com.netflix.request.lifecycle.RequestAttributesModule;

public final class MyAppModule extends AbstractModule {
    @Override
    protected void configure() {
        install(new RequestAttributesModule());
    }
}
```

##### Spring

Spring auto-configuration is defined by the `RequestAttributesAutoConfiguration` class.
You don't need to do anything aside from include the lifecycle library in the application dependencies.


## Issues / Questions

* Unfortunately the current Visitor is _not_ part of the `RequestContext`,
  and is not automatically passed along from clients to servers.
  For that reason applications should, early in their request processing,
  set the current Visitor as suggested above.

* There are a number of redundant fields in the `RequestContext`.
  The device type can be computed from the device ID,
  and the GMT offset value can be computed from the timezone name.
  How should these be handled in the `RequestAttributes`?
  Should computable fields be ignored?
  Should the builder fill in the computable fields (possibly dragging in other libraries like DTS)?

* There is some confusion about the difference between the `RequestContext` and the binding context that holds all `RequestVariable` values.
  In particular, the various "closure" methods actually capture and install the entire binding context,
  not just the `RequestContext`.
  That's probably the right behavior,
  but it should be spelled out.

## Project Resources

### Builds

| Build | Status |
|-------|--------|
| [Build Master](https://platform.builds.test.netflix.net/job/CPIEC-request-attrs-build-master) | ![Build Status](https://platform.builds.test.netflix.net/buildStatus/icon?job=CPIEC-request-attrs-build-master) |
| [Build Pull Request](https://platform.builds.test.netflix.net/job/CPIEC-request-attrs-build-pull-request) | ![Build Status](https://platform.builds.test.netflix.net/buildStatus/icon?job=CPIEC-request-attrs-build-pull-request) |
| [Release](https://platform.builds.test.netflix.net/job/CPIEC-request-attrs-release) | ![Build Status](https://platform.builds.test.netflix.net/buildStatus/icon?job=CPIEC-request-attrs-release) |
| [Update Dependencies](https://platform.builds.test.netflix.net/job/CPIEC-request-attrs-update-dependencies-lock) | ![Build Status](https://platform.builds.test.netflix.net/buildStatus/icon?job=CPIEC-request-attrs-update-dependencies-lock) |

<!--

- CI Pipeline Flowchart: [https://stash.corp.netflix.com/snippets/raw/551dc37898ae4a519402ce7527bbe6b1/Generated_ci_pipeline.txt](https://stash.corp.netflix.com/snippets/raw/551dc37898ae4a519402ce7527bbe6b1/Generated_ci_pipeline.txt)

Auto-generated by the nf-java-project generator @5.3.4:
https://stash.corp.netflix.com/projects/CPIE/repos/generator-nf-java-project

Owned by: <EMAIL>

-->
